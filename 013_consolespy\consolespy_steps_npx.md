ConsoleSpy - NPX 启动步骤

1. 安装Node.js和Chrome浏览器
- 访问 https://nodejs.org/ 下载并安装Node.js 18.0+
- 下载并安装Chrome浏览器
- npm通常随Node.js一起安装
- 验证安装：`node --version` 和 `npm --version`

2. 克隆源码并进入目录
- 运行 `git clone https://github.com/mgsrevolver/consolespy.git`
- 进入项目目录：`cd consolespy`

3. 安装依赖并运行设置
- 运行 `npm install` 安装项目依赖
- 运行 `./setup.sh` 执行设置脚本
- 等待安装完成

4. 安装浏览器扩展
- 访问Chrome Web Store安装ConsoleSpy扩展
- 或在Chrome中加载extension文件夹作为开发者扩展
- 确认扩展安装成功

5. 启动ConsoleSpy服务
- 运行 `npx supergateway --port 8766 --stdio "node console-spy-mcp.js"`
- 服务将在指定端口启动

6. 验证服务运行
- 确认服务正常启动
- 通过MCP客户端测试控制台监控功能
