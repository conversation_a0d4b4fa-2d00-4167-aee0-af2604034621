[project]
name = "daraja"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "boto3>=1.37.22",
    "httpx>=0.28.1",
    "ipykernel>=6.29.5",
    "langchain-mongodb>=0.6.0",
    "langchain-openai>=0.3.11",
    "mcp[cli]>=1.5.0",
    "pymongo>=4.11.3",
    "python-dotenv>=1.1.0",
    "unstructured-client>=0.30.6",
]

[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[tool.setuptools.packages.find]
include = ["daraja_endpoints*", "mpesa*", "database*", "unstructured*", "file_processing*"]
