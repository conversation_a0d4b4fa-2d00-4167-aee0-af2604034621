## 1. Setup & Requirements

### Prerequisites
- Node.js 18.0 or higher
- npm package manager
- Spotify API credentials (Client ID and Client Secret)

Install Node.js and global package:
```bash
# Install Node.js
# Visit https://nodejs.org to download and install the latest version

# Install package globally
npm install -g @thomaswawra/artistlens
```

Get Spotify API credentials:
1. Go to Spotify Developer Dashboard (https://developer.spotify.com/dashboard)
2. Create a new application
3. Get your Client ID and Client Secret

## 2. Configure Environment Variables

Configure the following environment variables in MCP NOW client:

**Configuration Details:**
- `SPOTIFY_CLIENT_ID`: Spotify API Client ID
- `SPOTIFY_CLIENT_SECRET`: Spotify API Client Secret

## 3. Launch the Server

After completing the above steps, click the **Install** button below to connect and start the service.

Launch command:
```bash
artistlens
```
