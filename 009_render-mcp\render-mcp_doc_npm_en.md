## 1. Setup & Requirements

### Prerequisites
- Node.js 18.0 or higher
- npm package manager
- Render.com API key

Install Node.js and global package:
```bash
# Install Node.js
# Visit https://nodejs.org to download and install the latest version

# Install package globally
npm install -g @niyogi/render-mcp
```

Get Render API key:
1. Go to Render Dashboard (https://dashboard.render.com/account/api-keys)
2. Create a new API key
3. Copy the generated API key

## 2. Configure Environment Variables

Configure the following environment variables in MCP NOW client:

**Configuration Details:**
- `RENDER_API_KEY`: Render.com API key

## 3. Launch the Server

After completing the above steps, click the **Install** button below to connect and start the service.

Launch command:
```bash
render-mcp start
```
