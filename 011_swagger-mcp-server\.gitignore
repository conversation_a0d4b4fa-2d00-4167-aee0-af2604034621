# 依赖目录
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log
pnpm-debug.log
.pnpm-store/

# 构建输出
dist/
build/
lib/
*.tsbuildinfo

# MCP特定文件
swagger-mcp-config.local.json
generated/
.mcp-cache/

# 环境变量
.env
.env.local
.env.*.local

# IDE和编辑器
.idea/
.vscode/
*.sublime-project
*.sublime-workspace
*.swp
*.swo
.vs/
*.iml

# 日志
logs/
*.log

# 操作系统
.DS_Store
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# 测试
coverage/
.nyc_output/

# 临时文件
.temp/
.tmp/
.cache/
.parcel-cache/

# API缓存目录
.api-cache/

# Swagger临时文件
swagger-output.json
swagger-ui/
api-docs.json

# 调试文件
.inspector/

# 本地配置
swagger-mcp-config.local.json