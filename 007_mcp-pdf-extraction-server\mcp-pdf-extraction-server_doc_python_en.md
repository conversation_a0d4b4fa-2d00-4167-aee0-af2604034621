## 1. Setup & Requirements

### Prerequisites
- Python 3.11 or higher
- pip package manager
- Git

Install required software:
```bash
# Install Python 3.11+
# Visit https://python.org to download and install

# Install Git
# Visit https://git-scm.com to download and install

# Clone project and install
git clone https://github.com/lh/mcp-pdf-extraction-server.git
cd mcp-pdf-extraction-server
pip install -e .
```

## 2. Configure Environment Variables

No additional configuration required.

## 3. Launch the Server

After completing the above steps, click the **Install** button below to connect and start the service.

Launch command:
```bash
python -m pdf_extraction
```
