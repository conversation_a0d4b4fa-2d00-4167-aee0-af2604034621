## 1. Setup & Requirements

### Prerequisites
- Python 3.8 or higher
- uv package manager
- Git

Install required software:
```bash
# Install Python 3.8+
# Visit https://python.org to download and install

# Install uv
pip install uv

# Install Git
# Visit https://git-scm.com to download and install

# Clone project and setup environment
git clone https://github.com/yourusername/UML-MCP-Server.git
cd UML-MCP-Server
python -m venv uml-mcp-venv
# Windows: uml-mcp-venv\Scripts\activate
# Linux/macOS: source uml-mcp-venv/bin/activate
pip install -r requirements.txt
```

## 2. Configure Environment Variables

Configure the following environment variables in MCP NOW client:

**Configuration Details:**
- `output_dir`: UML image output directory path (optional)

## 3. Launch the Server

After completing the above steps, click the **Install** button below to connect and start the service.

Launch command:
```bash
uv --directory /absolute/path/to/UML-MCP-Server run uml_mcp_server.py
```
