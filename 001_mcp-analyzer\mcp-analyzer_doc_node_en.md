## 1. Setup & Requirements

### Prerequisites
- Node.js 18.0 or higher
- npm package manager
- Git

Install required software:
```bash
# Install Node.js
# Visit https://nodejs.org to download and install the latest version

# Install Git
# Visit https://git-scm.com to download and install

# Clone project and install dependencies
git clone https://github.com/klara-research/MCP-Analyzer.git
cd MCP-Analyzer
npm i
npx tsc
```

## 2. Configure Environment Variables

No additional configuration required.

## 3. Launch the Server

After completing the above steps, click the **Install** button below to connect and start the service.

Launch command:
```bash
node /absolute/path/MCP-Analyzer/build
```
