## 1. 前置准备

### 先决条件
- Node.js 18.0 或更高版本
- npm 包管理器
- Brave Search API 密钥

安装 Node.js：
```bash
# 访问 https://nodejs.org 下载并安装最新版本
# 或使用包管理器安装，例如：
# Windows: winget install OpenJS.NodeJS
# macOS: brew install node
# Ubuntu: sudo apt install nodejs npm
```

获取 Brave Search API 密钥：
1. 访问 Brave Search API 官网注册账户
2. 创建新的 API 密钥
3. 复制生成的 API 密钥

## 2. 配置环境变量

在 MCP NOW 客户端中设置以下环境变量：

**配置说明：**
- `BRAVE_API_KEY`: Brave Search API 密钥

## 3. 启动服务

完成以上步骤后，点击下方的 **安装** 按钮连接并启动服务。

启动命令：
```bash
npx -y @smithery/cli install @arben-adm/brave-mcp-search --client claude
```
