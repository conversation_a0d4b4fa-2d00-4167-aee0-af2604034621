## 1. 前置准备

### 先决条件
- Node.js 18.0 或更高版本
- npm 包管理器
- Spotify API 凭据（Client ID 和 Client Secret）

安装 Node.js：
```bash
# 访问 https://nodejs.org 下载并安装最新版本
# 或使用包管理器安装，例如：
# Windows: winget install OpenJS.NodeJS
# macOS: brew install node
# Ubuntu: sudo apt install nodejs npm
```

获取 Spotify API 凭据：
1. 访问 Spotify Developer Dashboard (https://developer.spotify.com/dashboard)
2. 创建新应用
3. 获取 Client ID 和 Client Secret

## 2. 配置环境变量

在 MCP NOW 客户端中设置以下环境变量：

**配置说明：**
- `SPOTIFY_CLIENT_ID`: Spotify API Client ID
- `SPOTIFY_CLIENT_SECRET`: Spotify API Client Secret

## 3. 启动服务

完成以上步骤后，点击下方的 **安装** 按钮连接并启动服务。

启动命令：
```bash
npx -y @smithery/cli install @superseoworld/artistlens --client claude
```
