## 1. Setup & Requirements

### Prerequisites
- Python 3.11 or higher
- uv package manager
- Git
- Brave Search API key

Install required software:
```bash
# Install Python 3.11+
# Visit https://python.org to download and install

# Install uv
pip install uv

# Install Git
# Visit https://git-scm.com to download and install

# Clone project and setup environment
git clone https://github.com/your-username/brave-search-mcp.git
cd brave-search-mcp
uv venv
# Windows: .venv\Scripts\activate
# Linux/macOS: source .venv/bin/activate
uv pip install -r requirements.txt
```

Get Brave Search API key:
1. Visit Brave Search API website to register an account
2. Create a new API key
3. Copy the generated API key

## 2. Configure Environment Variables

Configure the following environment variables in MCP NOW client:

**Configuration Details:**
- `BRAVE_API_KEY`: Brave Search API key

## 3. Launch the Server

After completing the above steps, click the **Install** button below to connect and start the service.

Launch command:
```bash
uv --directory path-to/brave-mcp-search/src run server.py
```
