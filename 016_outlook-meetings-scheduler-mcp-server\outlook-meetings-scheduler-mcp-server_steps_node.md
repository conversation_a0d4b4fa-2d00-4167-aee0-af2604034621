Outlook Meetings Scheduler MCP Server - Node 启动步骤

1. 安装Node.js和npm
- 访问 https://nodejs.org/ 下载并安装Node.js 18.0+
- npm通常随Node.js一起安装
- 验证安装：`node --version` 和 `npm --version`

2. 克隆源码并进入目录
- 运行 `git clone https://github.com/anoopt/outlook-meetings-scheduler-mcp-server.git`
- 进入项目目录：`cd outlook-meetings-scheduler-mcp-server`

3. 安装依赖并构建项目
- 运行 `npm install` 安装项目依赖
- 运行 `npm run build` 构建项目
- 确认build目录生成成功

4. 设置Microsoft Azure应用
- 在Azure Portal中注册新应用
- 创建客户端密钥
- 授予必要权限（Microsoft Graph API > Application permissions > Calendars.ReadWrite, People.Read.All, User.ReadBasic.All）
- 记录Client ID、Client Secret和Tenant ID

5. 设置环境变量
- 设置 `CLIENT_ID` 为Azure应用的Client ID
- 设置 `CLIENT_SECRET` 为Azure应用的Client Secret
- 设置 `TENANT_ID` 为Azure租户ID
- 设置 `USER_EMAIL` 为用户邮箱地址

6. 启动Outlook MCP服务
- 运行 `node build/index.js`
- 确保使用构建后文件的路径

7. 验证服务运行
- 确认服务正常启动
- 通过MCP客户端测试Outlook会议调度功能
