## 1. 前置准备

### 先决条件
- Python 3.8 或更高版本
- uv 包管理器
- Git

安装所需软件：
```bash
# 安装 Python 3.8+
# 访问 https://python.org 下载并安装

# 安装 uv
pip install uv

# 安装 Git
# 访问 https://git-scm.com 下载并安装

# 克隆项目并设置环境
git clone https://github.com/yourusername/UML-MCP-Server.git
cd UML-MCP-Server
python -m venv uml-mcp-venv
# Windows: uml-mcp-venv\Scripts\activate
# Linux/macOS: source uml-mcp-venv/bin/activate
pip install -r requirements.txt
```

## 2. 配置环境变量

在 MCP NOW 客户端中设置以下环境变量：

**配置说明：**
- `output_dir`: UML 图像输出目录路径（可选）

## 3. 启动服务

完成以上步骤后，点击下方的 **安装** 按钮连接并启动服务。

启动命令：
```bash
uv --directory /absolute/path/to/UML-MCP-Server run uml_mcp_server.py
```
