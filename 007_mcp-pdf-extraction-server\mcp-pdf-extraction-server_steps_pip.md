MCP PDF Extraction Server - PIP 启动步骤

1. 安装Python和pip
- 访问 https://python.org/ 下载并安装Python 3.11+
- pip通常随Python一起安装
- 验证安装：`python --version` 和 `pip --version`

2. 克隆源码并进入目录
- 运行 `git clone https://github.com/lh/mcp-pdf-extraction-server.git`
- 进入项目目录：`cd mcp-pdf-extraction-server`

3. 安装项目依赖
- 运行 `pip install -e .` 安装项目及其依赖
- 等待安装完成
- 验证安装成功

4. 验证安装结果
- 运行 `pdf-extraction --help` 确认命令可用
- 检查是否有错误信息

5. 启动PDF提取服务
- 运行 `pdf-extraction`
- 服务将直接启动

6. 验证服务运行
- 确认服务正常启动
- 通过MCP客户端测试PDF提取功能

7. 测试PDF提取功能
- 准备测试PDF文件
- 验证文本提取功能

