## 1. 前置准备

### 先决条件
- Node.js 18.0 或更高版本
- npm 包管理器
- Python 3.8 或更高版本
- Instagram 账户凭据（Session ID、CSRF Token、DS User ID）

安装所需软件：
```bash
# 安装 Node.js
# 访问 https://nodejs.org 下载并安装最新版本

# 安装 Python
# 访问 https://python.org 下载并安装

# 全局安装包
npm install -g instagram-dm-mcp

# 运行设置脚本安装 Python 依赖
instagram-dm-mcp-setup
```

获取 Instagram 凭据：
1. 在 Chrome 中登录 Instagram
2. 右键点击页面，选择"检查"
3. 转到"Application"标签页
4. 点击左侧边栏中的"Cookies"
5. 复制 `sessionid`、`csrftoken` 和 `ds_user_id` 的值

## 2. 配置环境变量

在 MCP NOW 客户端中设置以下环境变量：

**配置说明：**
- `INSTAGRAM_SESSION_ID`: Instagram Session ID
- `INSTAGRAM_CSRF_TOKEN`: Instagram CSRF Token
- `INSTAGRAM_DS_USER_ID`: Instagram DS User ID

## 3. 启动服务

完成以上步骤后，点击下方的 **安装** 按钮连接并启动服务。

启动命令：
```bash
npx -y instagram-dm-mcp start
```
