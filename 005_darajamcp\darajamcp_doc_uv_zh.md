## 1. 前置准备

### 先决条件
- Python 3.12
- uv 包管理器
- Git
- Safaricom Daraja API 凭据（Consumer Key 和 Secret）

安装所需软件：
```bash
# 安装 Python 3.12
# 访问 https://python.org 下载并安装

# 安装 uv 包管理器
# Mac/Linux:
curl -LsSf https://astral.sh/uv/install.sh | sh
# Windows (PowerShell):
powershell -ExecutionPolicy ByPass -c "irm https://astral.sh/uv/install.ps1 | iex"

# 安装 Git
# 访问 https://git-scm.com 下载并安装

# 克隆项目并设置环境
git clone https://github.com/jameskanyiri/DarajaMCP.git
cd DarajaMCP
uv venv
# Windows: .venv\Scripts\activate
# Linux/macOS: source .venv/bin/activate
uv sync
```

获取 Safaricom Daraja API 凭据：
1. 访问 Safaricom Daraja API 开发者门户
2. 注册账户并创建应用
3. 获取 Consumer Key 和 Consumer Secret

## 2. 配置环境变量

在 MCP NOW 客户端中设置以下环境变量：

**配置说明：**
- `DARAJA_CONSUMER_KEY`: Safaricom Daraja API Consumer Key
- `DARAJA_CONSUMER_SECRET`: Safaricom Daraja API Consumer Secret
- `DARAJA_ENVIRONMENT`: 环境设置（sandbox 或 production）

## 3. 启动服务

完成以上步骤后，点击下方的 **安装** 按钮连接并启动服务。

启动命令：
```bash
uv --directory /absolute/path/to/DarajaMCP run main.py
```
