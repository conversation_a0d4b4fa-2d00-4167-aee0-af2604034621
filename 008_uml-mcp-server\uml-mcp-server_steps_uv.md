UML MCP Server - UV 启动步骤

1. 安装Python和uv
- 访问 https://python.org/ 下载并安装Python 3.8+
- 运行 `pip install uv` 安装uv包管理器
- 验证安装：`python --version` 和 `uv --version`

2. 克隆源码并设置环境
- 运行 `git clone https://github.com/Swayingleaves/uml-mcp-server.git`
- 进入项目目录：`cd uml-mcp-server`
- 创建虚拟环境：`python -m venv uml-mcp-venv`

3. 激活环境并安装依赖
- 激活虚拟环境：`uml-mcp-venv\Scripts\activate` (Windows) 或 `source uml-mcp-venv/bin/activate` (Linux/Mac)
- 安装依赖：`pip install -r requirements.txt`

4. 配置输出目录（可选）
- 创建UML图像输出目录
- 设置 `output_dir` 环境变量（可选）
- 确保目录有写入权限

5. 启动UML MCP服务
- 运行 `uv --directory /absolute/path/to/UML-MCP-Server run uml_mcp_server.py`
- 确保使用项目的绝对路径

6. 验证服务运行
- 确认服务正常启动
- 通过MCP客户端测试UML生成功能

7. 测试UML功能
- 测试UML图生成
- 验证图像输出功能

