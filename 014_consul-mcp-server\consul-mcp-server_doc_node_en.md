## 1. Setup & Requirements

### Prerequisites
- Node.js 18.0 or higher
- npm package manager
- Git
- Consul server

Install required software:
```bash
# Install Node.js
# Visit https://nodejs.org to download and install the latest version

# Install Git
# Visit https://git-scm.com to download and install

# Clone project and install dependencies
git clone https://github.com/kocierik/consul-mcp-server.git
cd consul-mcp-server
npm install
npm run build
```

## 2. Configure Environment Variables

Configure the following environment variables in MCP NOW client:

**Configuration Details:**
- `CONSUL_HOST`: Consul server host (default: localhost)
- `CONSUL_PORT`: Consul server port (default: 8500)

## 3. Launch the Server

After completing the above steps, click the **Install** button below to connect and start the service.

Launch command:
```bash
node build/index.js
```
