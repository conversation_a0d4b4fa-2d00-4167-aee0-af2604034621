MCP Analyzer - Node 启动步骤

1. 安装Node.js和npm
- 访问 https://nodejs.org/ 下载并安装Node.js 18.0+
- npm通常随Node.js一起安装
- 验证安装：`node --version` 和 `npm --version`

2. 克隆源码并进入目录
- 运行 `git clone https://github.com/klara-research/MCP-Analyzer.git`
- 进入项目目录：`cd MCP-Analyzer`

3. 安装依赖并构建项目
- 运行 `npm install` 安装项目依赖
- 运行 `npx tsc` 编译TypeScript代码
- 确认build目录生成成功

4. 验证构建结果
- 检查build目录中的文件
- 确认编译过程无错误

5. 启动服务
- 运行 `node /absolute/path/MCP-Analyzer/build`
- 确保使用构建后文件的绝对路径

6. 验证服务运行
- 确认服务正常启动
- 通过MCP客户端测试分析功能

