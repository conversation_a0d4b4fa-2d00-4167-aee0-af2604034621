## 1. 前置准备

### 先决条件
- Python 3.11 或更高版本
- uv 包管理器
- Git
- Brave Search API 密钥

安装所需软件：
```bash
# 安装 Python 3.11+
# 访问 https://python.org 下载并安装

# 安装 uv
pip install uv

# 安装 Git
# 访问 https://git-scm.com 下载并安装

# 克隆项目并设置环境
git clone https://github.com/your-username/brave-search-mcp.git
cd brave-search-mcp
uv venv
# Windows: .venv\Scripts\activate
# Linux/macOS: source .venv/bin/activate
uv pip install -r requirements.txt
```

获取 Brave Search API 密钥：
1. 访问 Brave Search API 官网注册账户
2. 创建新的 API 密钥
3. 复制生成的 API 密钥

## 2. 配置环境变量

在 MCP NOW 客户端中设置以下环境变量：

**配置说明：**
- `BRAVE_API_KEY`: Brave Search API 密钥

## 3. 启动服务

完成以上步骤后，点击下方的 **安装** 按钮连接并启动服务。

启动命令：
```bash
uv --directory path-to/brave-mcp-search/src run server.py
```
