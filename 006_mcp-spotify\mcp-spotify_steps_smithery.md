MCP Spotify - Smithery 启动步骤

1. 安装Node.js和npm
- 访问 https://nodejs.org/ 下载并安装Node.js 18.0+
- npm通常随Node.js一起安装
- 验证安装：`node --version` 和 `npm --version`

2. 获取Spotify API凭据
- 访问 Spotify Developer Dashboard (https://developer.spotify.com/dashboard)
- 创建新应用
- 获取Client ID和Client Secret
- 配置重定向URI（如果需要）

3. 设置环境变量
- 设置 `SPOTIFY_CLIENT_ID` 为Client ID
- 设置 `SPOTIFY_CLIENT_SECRET` 为Client Secret
- 确认环境变量设置正确

4. 验证Smithery CLI工具
- 运行 `npx @smithery/cli --version` 确认工具可用
- Smithery CLI将自动下载

5. 使用Smithery安装Spotify MCP服务
- 运行 `npx -y @smithery/cli install @superseoworld/artistlens --client claude`
- Smithery将自动配置和安装服务

6. 验证服务运行
- 确认服务正常启动
- 通过MCP客户端测试Spotify功能

7. 测试Spotify功能
- 测试艺术家搜索
- 验证音乐数据获取

