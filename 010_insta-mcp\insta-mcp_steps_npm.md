Instagram MCP - NPM 启动步骤

1. 安装Node.js、Python和npm
- 访问 https://nodejs.org/ 下载并安装Node.js 18.0+
- 访问 https://python.org/ 下载并安装Python 3.8+
- npm通常随Node.js一起安装
- 验证安装：`node --version`、`npm --version` 和 `python --version`

2. 全局安装Instagram MCP包
- 运行 `npm install -g instagram-dm-mcp`
- 运行 `instagram-dm-mcp-setup` 安装Python依赖
- 等待安装完成

3. 获取Instagram凭据
- 在Chrome中登录Instagram
- 右键点击页面，选择"检查"
- 转到"Application"标签页，点击"Cookies"
- 复制 `sessionid`、`csrftoken` 和 `ds_user_id` 的值

4. 设置环境变量
- 设置 `INSTAGRAM_SESSION_ID` 为Session ID
- 设置 `INSTAGRAM_CSRF_TOKEN` 为CSRF Token
- 设置 `INSTAGRAM_DS_USER_ID` 为DS User ID

5. 启动Instagram MCP服务
- 运行 `npx -y instagram-dm-mcp start`
- 服务将自动启动

6. 验证服务运行
- 确认服务正常启动
- 通过MCP客户端测试Instagram功能
