## 1. Setup & Requirements

### Prerequisites
- Python 3.12
- uv package manager
- Git
- Safaricom Daraja API credentials (Consumer Key and Secret)

Install required software:
```bash
# Install Python 3.12
# Visit https://python.org to download and install

# Install uv package manager
# Mac/Linux:
curl -LsSf https://astral.sh/uv/install.sh | sh
# Windows (PowerShell):
powershell -ExecutionPolicy ByPass -c "irm https://astral.sh/uv/install.ps1 | iex"

# Install Git
# Visit https://git-scm.com to download and install

# Clone project and setup environment
git clone https://github.com/jameskanyiri/DarajaMCP.git
cd DarajaMCP
uv venv
# Windows: .venv\Scripts\activate
# Linux/macOS: source .venv/bin/activate
uv sync
```

Get Safaricom Daraja API credentials:
1. Visit Safaricom Daraja API developer portal
2. Register account and create application
3. Obtain Consumer Key and Consumer Secret

## 2. Configure Environment Variables

Configure the following environment variables in MCP NOW client:

**Configuration Details:**
- `DARAJA_CONSUMER_KEY`: Safaricom Daraja API Consumer Key
- `DARAJA_CONSUMER_SECRET`: Safaricom Daraja API Consumer Secret
- `DARAJA_ENVIRONMENT`: Environment setting (sandbox or production)

## 3. Launch the Server

After completing the above steps, click the **Install** button below to connect and start the service.

Launch command:
```bash
uv --directory /absolute/path/to/DarajaMCP run main.py
```
