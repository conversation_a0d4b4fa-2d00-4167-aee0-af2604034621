## 1. Setup & Requirements

### Prerequisites
- Node.js 18.0 or higher
- Atlassian account with access to Confluence Cloud

Install Node.js and global package:
```bash
# Install Node.js
# Visit https://nodejs.org to download and install the latest version

# Install package globally
npm install -g @aashari/mcp-server-atlassian-confluence
```

Get Atlassian API Token:
1. Go to https://id.atlassian.com/manage-profile/security/api-tokens
2. Click **Create API token**
3. Enter a descriptive label (e.g., mcp-confluence-access)
4. Click **Create**
5. Copy the generated API token (shown only once)

## 2. Configure Environment Variables

Configure the following environment variables in MCP NOW client:

**Configuration Details:**
- `ATLASSIAN_SITE_NAME`: Confluence site name (e.g., mycompany for mycompany.atlassian.net)
- `ATLASSIAN_USER_EMAIL`: Atlassian account email
- `ATLASSIAN_API_TOKEN`: API token obtained from step 1

## 3. Launch the Server

After completing the above steps, click the **Install** button below to connect and start the service.

Launch command:
```bash
mcp-atlassian-confluence
```
