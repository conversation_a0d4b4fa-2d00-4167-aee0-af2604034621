# Generated by https://smithery.ai. See: https://smithery.ai/docs/config#dockerfile
FROM node:lts-alpine

WORKDIR /app

# Copy package files
COPY package.json pnpm-lock.yaml ./

# Install dependencies without running lifecycle scripts
RUN npm install --ignore-scripts

# Copy rest of the application
COPY . .

# Build the project
RUN npm run build

# Expose port if needed. MCP transport is typically stdio, so port expose might not be needed.

CMD ["node", "start-server.js"]
