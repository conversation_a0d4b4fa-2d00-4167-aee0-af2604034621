[project]
name = "pdf-extraction"
version = "0.1.0"
description = "MCP server to extract contents from PDF files"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
 "mcp>=1.2.0",
 "pypdf2>=3.0.1",
 "pytesseract>=0.3.10",
 "Pillow>=10.0.0",
 "pydantic>=2.10.1,<3.0.0",
 "pymupdf>=1.24.0"
]

[build-system]
requires = [ "hatchling",]
build-backend = "hatchling.build"

[project.scripts]
pdf-extraction = "pdf_extraction:main"
