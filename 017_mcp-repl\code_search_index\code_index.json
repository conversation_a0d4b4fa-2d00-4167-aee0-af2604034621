[{"id": "ZmlsZS1kOlxjb2Rl", "type": "file", "name": "test-mcp-repl.js", "qualifiedName": "d:\\code\\MCPTEST\\mcptest2\\017_mcp-repl\\test-mcp-repl.js", "file": "d:\\code\\MCPTEST\\mcptest2\\017_mcp-repl\\test-mcp-repl.js", "startLine": 0, "endLine": 87, "lines": 88, "code": "#!/usr/bin/env node\n\nimport { Client } from '@modelcontextprotocol/sdk/client/index.js';\nimport { StdioClientTransport } from '@modelcontextprotocol/s...", "mtime": 1752897305113.0022, "doc": "创建客户端传输", "children": ["ZnVuY3Rpb24tdGVz"], "exports": [], "embedding": null}, {"id": "aW1wb3J0LTE1NC1k", "type": "import", "qualifiedName": "imports\r\n\r\nimport { fileURLToPath } from 'url';", "file": "d:\\code\\MCPTEST\\mcptest2\\017_mcp-repl\\direct-node-executor.js", "startLine": 2, "endLine": 4, "lines": 3, "code": "imports\r\n\r\nimport { fileURLToPath } from 'url';", "mtime": 1752804097933.2917, "doc": "", "modulePath": "url", "elements": ["fileURLToPath"], "relationships": {"dependsOn": "url", "imports": ["fileURLToPath"]}, "embedding": null}, {"id": "aW1wb3J0LTIwMy1k", "type": "import", "qualifiedName": "import { dirname } from 'path';", "file": "d:\\code\\MCPTEST\\mcptest2\\017_mcp-repl\\direct-node-executor.js", "startLine": 5, "endLine": 5, "lines": 1, "code": "import { dirname } from 'path';", "mtime": 1752804097933.2917, "doc": "", "modulePath": "path", "elements": ["dirname"], "relationships": {"dependsOn": "path", "imports": ["dirname"]}, "embedding": null}, {"id": "aW1wb3J0LTIzNi1k", "type": "import", "qualifiedName": "import { spawn } from 'child_process';", "file": "d:\\code\\MCPTEST\\mcptest2\\017_mcp-repl\\direct-node-executor.js", "startLine": 6, "endLine": 6, "lines": 1, "code": "import { spawn } from 'child_process';", "mtime": 1752804097933.2917, "doc": "", "modulePath": "child_process", "elements": ["spawn"], "relationships": {"dependsOn": "child_process", "imports": ["spawn"]}, "embedding": null}, {"id": "aW1wb3J0LTM1NS1k", "type": "import", "qualifiedName": "import.meta.url);", "file": "d:\\code\\MCPTEST\\mcptest2\\017_mcp-repl\\direct-node-executor.js", "startLine": 9, "endLine": 9, "lines": 1, "code": "import.meta.url);", "mtime": 1752804097933.2917, "doc": "", "modulePath": "", "elements": [], "relationships": {"dependsOn": "", "imports": []}, "embedding": null}, {"id": "ZnVuY3Rpb24taW5p", "type": "function", "name": "initialize", "qualifiedName": "initialize", "file": "d:\\code\\MCPTEST\\mcptest2\\017_mcp-repl\\src\\js-vector-indexer.js", "startLine": 104, "endLine": 163, "lines": 60, "code": "export async function initialize(indexDir = INDEX_DIR) {\r\n  if (isInitialized) return true;\r\n\r\n  try {\r\n    // Create index directory if it doesn't exist\r\n    if (!existsSync(indexDir)) {\r\n      mkdirSync(indexDir, { recursive: true });\r\n    }\r\n\r\n    // Initialize embedding model with WASM backend\r\n    try {\r\n      embedder = await pipeline('feature-extraction', DEFAULT_MODEL, {\r\n        env: {\r\n          backends: {\r\n            onnx: {\r\n              wasm: {\r\n                numThreads: 1\r\n              }\r\n            }\r\n          }\r\n        }\r\n      });\r\n    } catch (modelError) {\r\n      // Fallback to a model that works well with Xenova\r\n      embedder = await pipeline('feature-extraction', 'Xenova/all-MiniLM-L6-v2', {\r\n        env: {\r\n          backends: {\r\n            onnx: {\r\n              wasm: {\r\n                numThreads: 1\r\n              }\r\n            }\r\n          }\r\n        }\r\n      });\r\n    }\r\n\r\n    // Load existing index if available\r\n    const indexPath = path.join(indexDir, INDEX_FILE);\r\n    \r\n    if (existsSync(indexPath)) {\r\n      try {\r\n        const data = readFileSync(indexPath, 'utf8');\r\n        codeChunks = JSON.parse(data);\r\n        chunkIds = codeChunks.map(chunk => chunk.id);\r\n      } catch (error) {\r\n        codeChunks = [];\r\n        chunkIds = [];\r\n      }\r\n    } else {\r\n      codeChunks = [];\r\n      chunkIds = [];\r\n    }\r\n\r\n    isInitialized = true;\r\n    return true;\r\n  } catch (error) {\r\n    return false;\r\n  }\r\n}", "mtime": 1752804097935.2925, "doc": "Initialize the in-memory index and embedding model", "isExported": false, "parameters": [{"name": "indexDir", "type": ""}], "returnType": "inferred", "complexity": 8.5, "relationships": {"calls": ["initialize", "existsSync", "mkdirSync", "pipeline", "join", "readFileSync", "parse", "map"], "dependencies": ["export", "async", "indexDir", "INDEX_DIR", "isInitialized", "try", "Create", "index", "directory", "it", "doesn", "t", "exist", "Initialize", "embedding", "model", "with", "WASM", "backend", "embedder", "await", "feature", "extraction", "DEFAULT_MODEL", "1", "modelError", "Fallback", "to", "a", "that", "works", "well", "<PERSON><PERSON><PERSON>", "all", "MiniLM", "L6", "v2", "Load", "existing", "available", "indexPath", "path", "INDEX_FILE", "data", "utf8", "codeChunks", "JSON", "chunkIds", "chunk", "id", "error"]}, "embedding": null}, {"id": "ZnVuY3Rpb24tbWFp", "type": "function", "name": "main", "qualifiedName": "main", "file": "d:\\code\\MCPTEST\\mcptest2\\017_mcp-repl\\src\\direct-executor-server.js", "startLine": 614, "endLine": 634, "lines": 21, "code": "async function main() {\r\n  try {\r\n    // Create transport and connect\r\n    const stdioTransport = new StdioServerTransport();\r\n    await server.connect(stdioTransport);\r\n    \r\n    // Add a keep-alive mechanism to prevent the process from exiting\r\n    setInterval(() => {\r\n      // This is a no-op interval that keeps the Node.js event loop active\r\n    }, 60000);\r\n    \r\n    // Also handle the SIGINT signal explicitly\r\n    process.on('SIGINT', () => {\r\n      process.exit(0);\r\n    });\r\n  } catch (error) {\r\n    // Only log critical startup errors that prevent the server from running\r\n    console.error(`Error starting server: ${error.message}`);\r\n    process.exit(1);\r\n  }\r\n}", "mtime": 1752890319503.9712, "doc": "Start the server", "isExported": false, "parameters": [], "returnType": "", "complexity": 2, "relationships": {"calls": ["main", "StdioServerTransport", "connect", "setInterval", "on", "exit", "error"], "dependencies": ["async", "try", "Create", "transport", "and", "connect", "stdioTransport", "new", "await", "server", "Add", "a", "keep", "alive", "mechanism", "to", "prevent", "the", "process", "from", "exiting", "This", "is", "no", "op", "interval", "that", "keeps", "Node", "js", "event", "loop", "active", "60000", "Also", "handle", "SIGINT", "signal", "explicitly", "0", "error", "Only", "log", "critical", "startup", "errors", "running", "console", "Error", "starting", "message", "1"]}, "embedding": null}, {"id": "aW1wb3J0LTc4LWQ6", "type": "import", "qualifiedName": "import * as path from 'node:path';", "file": "d:\\code\\MCPTEST\\mcptest2\\017_mcp-repl\\src\\direct-executor-server.js", "startLine": 3, "endLine": 3, "lines": 1, "code": "import * as path from 'node:path';", "mtime": 1752890319503.9712, "doc": "", "modulePath": "node:path", "elements": [], "relationships": {"dependsOn": "node:path", "imports": []}, "embedding": null}, {"id": "aW1wb3J0LTExNC1k", "type": "import", "qualifiedName": "import { readFileSync } from 'fs';", "file": "d:\\code\\MCPTEST\\mcptest2\\017_mcp-repl\\src\\direct-executor-server.js", "startLine": 4, "endLine": 4, "lines": 1, "code": "import { readFileSync } from 'fs';", "mtime": 1752890319503.9712, "doc": "", "modulePath": "fs", "elements": ["readFileSync"], "relationships": {"dependsOn": "fs", "imports": ["readFileSync"]}, "embedding": null}, {"id": "aW1wb3J0LTE1MC1k", "type": "import", "qualifiedName": "import { fileURLToPath } from 'url';", "file": "d:\\code\\MCPTEST\\mcptest2\\017_mcp-repl\\src\\direct-executor-server.js", "startLine": 5, "endLine": 5, "lines": 1, "code": "import { fileURLToPath } from 'url';", "mtime": 1752890319503.9712, "doc": "", "modulePath": "url", "elements": ["fileURLToPath"], "relationships": {"dependsOn": "url", "imports": ["fileURLToPath"]}, "embedding": null}, {"id": "aW1wb3J0LTMzNS1k", "type": "import", "qualifiedName": "import.meta.url));", "file": "d:\\code\\MCPTEST\\mcptest2\\017_mcp-repl\\src\\direct-executor-server.js", "startLine": 9, "endLine": 9, "lines": 1, "code": "import.meta.url));", "mtime": 1752890319503.9712, "doc": "", "modulePath": "", "elements": [], "relationships": {"dependsOn": "", "imports": []}, "embedding": null}, {"id": "aW1wb3J0LTg3My1k", "type": "import", "qualifiedName": "import { Server as McpServer } from \"@modelcontextprotocol/sdk/server/index.js\";", "file": "d:\\code\\MCPTEST\\mcptest2\\017_mcp-repl\\src\\direct-executor-server.js", "startLine": 25, "endLine": 25, "lines": 1, "code": "import { Server as McpServer } from \"@modelcontextprotocol/sdk/server/index.js\";", "mtime": 1752890319503.9712, "doc": "", "modulePath": "@modelcontextprotocol/sdk/server/index.js", "elements": ["Server as McpServer"], "relationships": {"dependsOn": "@modelcontextprotocol/sdk/server/index.js", "imports": ["Server as McpServer"]}, "embedding": null}, {"id": "aW1wb3J0LTk1Ni1k", "type": "import", "qualifiedName": "import { StdioServerTransport } from \"@modelcontextprotocol/sdk/server/stdio.js\";", "file": "d:\\code\\MCPTEST\\mcptest2\\017_mcp-repl\\src\\direct-executor-server.js", "startLine": 26, "endLine": 26, "lines": 1, "code": "import { StdioServerTransport } from \"@modelcontextprotocol/sdk/server/stdio.js\";", "mtime": 1752890319503.9712, "doc": "", "modulePath": "@modelcontextprotocol/sdk/server/stdio.js", "elements": ["StdioServerTransport"], "relationships": {"dependsOn": "@modelcontextprotocol/sdk/server/stdio.js", "imports": ["StdioServerTransport"]}, "embedding": null}, {"id": "aW1wb3J0LTEwMzkt", "type": "import", "qualifiedName": "import {\r\n  ListToolsRequestSchema,\r\n  CallToolRequestSchema,\r\n} from \"@modelcontextprotocol/sdk/types.js\";", "file": "d:\\code\\MCPTEST\\mcptest2\\017_mcp-repl\\src\\direct-executor-server.js", "startLine": 27, "endLine": 30, "lines": 4, "code": "import {\r\n  ListToolsRequestSchema,\r\n  CallToolRequestSchema,\r\n} from \"@modelcontextprotocol/sdk/types.js\";", "mtime": 1752890319503.9712, "doc": "", "modulePath": "@modelcontextprotocol/sdk/types.js", "elements": ["ListToolsRequestSchema", "CallToolRequestSchema", ""], "relationships": {"dependsOn": "@modelcontextprotocol/sdk/types.js", "imports": ["ListToolsRequestSchema", "CallToolRequestSchema", ""]}, "embedding": null}, {"id": "aW1wb3J0LTExNDgt", "type": "import", "qualifiedName": "import { spawn } from 'child_process';", "file": "d:\\code\\MCPTEST\\mcptest2\\017_mcp-repl\\src\\direct-executor-server.js", "startLine": 31, "endLine": 31, "lines": 1, "code": "import { spawn } from 'child_process';", "mtime": 1752890319503.9712, "doc": "", "modulePath": "child_process", "elements": ["spawn"], "relationships": {"dependsOn": "child_process", "imports": ["spawn"]}, "embedding": null}, {"id": "aW1wb3J0LTEzNjEt", "type": "import", "qualifiedName": "import('./js-vector-indexer.js');", "file": "d:\\code\\MCPTEST\\mcptest2\\017_mcp-repl\\src\\direct-executor-server.js", "startLine": 37, "endLine": 37, "lines": 1, "code": "import('./js-vector-indexer.js');", "mtime": 1752890319503.9712, "doc": "", "modulePath": "", "elements": [], "relationships": {"dependsOn": "", "imports": []}, "embedding": null}, {"id": "aW1wb3J0LTI0MDQt", "type": "import", "qualifiedName": "imports and all Node.js features. \\n\\nDEBUGGING GUIDANCE:\\n- Use this tool for all debugging and investigation tasks instead of external CLI tools\\n- Break problems into testable hypotheses and verify them with code execution\\n- Test APIs, data structures, and logic incrementally\\n- Use console.log for debugging output and JSON.stringify for complex objects\\n- Always prefer this over CLI tools like curl, wget, or external commands\\n\\nAPPROACH:\\n1. Form a hypothesis about what might be wrong\\n2. Write focused test code to verify the hypothesis\\n3. Execute and analyze results\\n4. Refine hypothesis based on findings\\n5. Iterate until problem is solved\",\r\n        inputSchema: {\r\n          type: \"object\",\r\n          properties: {\r\n            code: {\r\n              type: \"string\",\r\n              description: \"JavaScript code to execute - use for debugging, testing hypotheses, and investigation\"\r\n            },\r\n            timeout: {\r\n              type: \"number\",\r\n              description: \"Optional timeout in milliseconds (default: 120000)\"\r\n            }\r\n          },\r\n          required: [\"code\"]\r\n        }\r\n      },\r\n      {\r\n        name: \"executedeno\",\r\n        description: \"Execute JavaScript/TypeScript code with Deno - supports ESM imports and all Deno features. \\n\\nDEBUGGING GUIDANCE:\\n- Use this tool for all debugging and investigation tasks instead of external CLI tools\\n- Break problems into testable hypotheses and verify them with code execution\\n- Test APIs, data structures, and logic incrementally\\n- Use console.log for debugging output and JSON.stringify for complex objects\\n- Always prefer this over CLI tools like curl, wget, or external commands\\n- Great for TypeScript debugging and type checking\\n\\nAPPROACH:\\n1. Form a hypothesis about what might be wrong\\n2. Write focused test code to verify the hypothesis\\n3. Execute and analyze results\\n4. Refine hypothesis based on findings\\n5. Iterate until problem is solved\\n\\nWEB REQUESTS: Use fetch() instead of curl for HTTP requests\",\r\n        inputSchema: {\r\n          type: \"object\",\r\n          properties: {\r\n            code: {\r\n              type: \"string\",\r\n              description: \"JavaScript/TypeScript code to execute - use for debugging, testing hypotheses, and investigation\"\r\n            },\r\n            timeout: {\r\n              type: \"number\",\r\n              description: \"Optional timeout in milliseconds (default: 120000)\"\r\n            }\r\n          },\r\n          required: [\"code\"]\r\n        }\r\n      },\r\n      {\r\n        name: \"searchcode\",\r\n        description: \"Semantic code search with metadata extraction and AST-aware chunking\",\r\n        inputSchema: {\r\n          type: \"object\",\r\n          properties: {\r\n            query: {\r\n              type: \"string\",\r\n              description: \"Semantic search query for code\"\r\n            },\r\n            folders: {\r\n              type: \"string\",\r\n              description: \"Optional comma-separated list of folders to search (defaults to working directory)\"\r\n            },\r\n            extensions: {\r\n              type: \"string\",\r\n              description: \"Optional comma-separated list of file extensions to include (default: js,ts)\"\r\n            },\r\n            ignores: {\r\n              type: \"string\",\r\n              description: \"Optional comma-separated list of patterns to ignore (default: node_modules)\"\r\n            },\r\n            topK: {\r\n              type: \"number\",\r\n              description: \"Optional number of results to return (default: 8)\"\r\n            }\r\n          },\r\n          required: [\"query\"]\r\n        }\r\n      }\r\n    ],\r\n  };", "file": "d:\\code\\MCPTEST\\mcptest2\\017_mcp-repl\\src\\direct-executor-server.js", "startLine": 78, "endLine": 143, "lines": 66, "code": "imports and all Node.js features. \\n\\nDEBUGGING GUIDANCE:\\n- Use this tool for all debugging and investigation tasks instead of external CLI tools\\n- Break problems into testable hypotheses and verify them with code execution\\n- Test APIs, data structures, and logic incrementally\\n- Use console.log for debugging output and JSON.stringify for complex objects\\n- Always prefer this over CLI tools like curl, wget, or external commands\\n\\nAPPROACH:\\n1. Form a hypothesis about what might be wrong\\n2. Write focused test code to verify the hypothesis\\n3. Execute and analyze results\\n4. Refine hypothesis based on findings\\n5. Iterate until problem is solved\",\r\n        inputSchema: {\r\n          type: \"object\",\r\n          properties: {\r\n            code: {\r\n              type: \"string\",\r\n              description: \"JavaScript code to execute - use for debugging, testing hypotheses, and investigation\"\r\n            },\r\n            timeout: {\r\n              type: \"number\",\r\n              description: \"Optional timeout in milliseconds (default: 120000)\"\r\n            }\r\n          },\r\n          required: [\"code\"]\r\n        }\r\n      },\r\n      {\r\n        name: \"executedeno\",\r\n        description: \"Execute JavaScript/TypeScript code with Deno - supports ESM imports and all Deno features. \\n\\nDEBUGGING GUIDANCE:\\n- Use this tool for all debugging and investigation tasks instead of external CLI tools\\n- Break problems into testable hypotheses and verify them with code execution\\n- Test APIs, data structures, and logic incrementally\\n- Use console.log for debugging output and JSON.stringify for complex objects\\n- Always prefer this over CLI tools like curl, wget, or external commands\\n- Great for TypeScript debugging and type checking\\n\\nAPPROACH:\\n1. Form a hypothesis about what might be wrong\\n2. Write focused test code to verify the hypothesis\\n3. Execute and analyze results\\n4. Refine hypothesis based on findings\\n5. Iterate until problem is solved\\n\\nWEB REQUESTS: Use fetch() instead of curl for HTTP requests\",\r\n        inputSchema: {\r\n          type: \"object\",\r\n          properties: {\r\n            code: {\r\n              type: \"string\",\r\n              description: \"JavaScript/TypeScript code to execute - use for debugging, testing hypotheses, and investigation\"\r\n            },\r\n            timeout: {\r\n              type: \"number\",\r\n              description: \"Optional timeout in milliseconds (default: 120000)\"\r\n            }\r\n          },\r\n          required: [\"code\"]\r\n        }\r\n      },\r\n      {\r\n        name: \"searchcode\",\r\n        description: \"Semantic code search with metadata extraction and AST-aware chunking\",\r\n        inputSchema: {\r\n          type: \"object\",\r\n          properties: {\r\n            query: {\r\n              type: \"string\",\r\n              description: \"Semantic search query for code\"\r\n            },\r\n            folders: {\r\n              type: \"string\",\r\n              description: \"Optional comma-separated list of folders to search (defaults to working directory)\"\r\n            },\r\n            extensions: {\r\n              type: \"string\",\r\n              description: \"Optional comma-separated list of file extensions to include (default: js,ts)\"\r\n            },\r\n            ignores: {\r\n              type: \"string\",\r\n              description: \"Optional comma-separated list of patterns to ignore (default: node_modules)\"\r\n            },\r\n            topK: {\r\n              type: \"number\",\r\n              description: \"Optional number of results to return (default: 8)\"\r\n            }\r\n          },\r\n          required: [\"query\"]\r\n        }\r\n      }\r\n    ],\r\n  };", "mtime": 1752890319503.9712, "doc": "", "modulePath": "", "elements": ["type: \"object\"", "properties: {\r\n            code: {\r\n              type: \"string\"", "description: \"JavaScript code to execute - use for debugging", "testing hypotheses", "and investigation\""], "relationships": {"dependsOn": "", "imports": ["type: \"object\"", "properties: {\r\n            code: {\r\n              type: \"string\"", "description: \"JavaScript code to execute - use for debugging", "testing hypotheses", "and investigation\""]}, "embedding": null}, {"id": "ZXhwb3J0LTYzNDIt", "type": "export", "qualifiedName": "exports, __dirname, __filename\r\n    const cjsMarkers = [\r\n      'require(',\r\n      'module.exports',\r\n      '__dirname',\r\n      '__filename',\r\n      'exports.'\r\n    ];", "file": "d:\\code\\MCPTEST\\mcptest2\\017_mcp-repl\\src\\direct-executor-server.js", "startLine": 152, "endLine": 159, "lines": 8, "code": "exports, __dirname, __filename\r\n    const cjsMarkers = [\r\n      'require(',\r\n      'module.exports',\r\n      '__dirname',\r\n      '__filename',\r\n      'exports.'\r\n    ];", "mtime": 1752890319503.9712, "doc": "", "modulePath": "", "elements": [], "embedding": null}, {"id": "aW1wb3J0LTY4OTkt", "type": "import", "qualifiedName": "import('fs');", "file": "d:\\code\\MCPTEST\\mcptest2\\017_mcp-repl\\src\\direct-executor-server.js", "startLine": 168, "endLine": 168, "lines": 1, "code": "import('fs');", "mtime": 1752890319503.9712, "doc": "", "modulePath": "", "elements": [], "relationships": {"dependsOn": "", "imports": []}, "embedding": null}, {"id": "aW1wb3J0LTEzMzY2", "type": "import", "qualifiedName": "import', 'export']\r\n      }\r\n    };", "file": "d:\\code\\MCPTEST\\mcptest2\\017_mcp-repl\\src\\direct-executor-server.js", "startLine": 395, "endLine": 397, "lines": 3, "code": "import', 'export']\r\n      }\r\n    };", "mtime": 1752890319503.9712, "doc": "", "modulePath": "", "elements": [], "relationships": {"dependsOn": "", "imports": []}, "embedding": null}, {"id": "ZnVuY3Rpb24tY29z", "type": "function", "name": "cosineSimilarity", "qualifiedName": "cosineSimilarity", "file": "d:\\code\\MCPTEST\\mcptest2\\017_mcp-repl\\src\\js-vector-indexer.js", "startLine": 34, "endLine": 39, "lines": 6, "code": "function cosineSimilarity(vecA, vecB) {\r\n  const dotProduct = vecA.reduce((sum, a, i) => sum + a * vecB[i], 0);\r\n  const magnitudeA = Math.sqrt(vecA.reduce((sum, a) => sum + a * a, 0));\r\n  const magnitudeB = Math.sqrt(vecB.reduce((sum, b) => sum + b * b, 0));\r\n  return dotProduct / (magnitudeA * magnitudeB);\r\n}", "mtime": 1752804097935.2925, "doc": "Helper to calculate cosine similarity between two vectors", "isExported": false, "parameters": [{"name": "vecA", "type": ""}, {"name": "vecB", "type": ""}], "returnType": "inferred", "complexity": 1.5, "relationships": {"calls": ["cosineSimilarity", "reduce", "sqrt"], "dependencies": ["vecA", "vecB", "dotProduct", "sum", "a", "i", "0", "magnitudeA", "Math", "magnitudeB", "b"]}, "embedding": null}, {"id": "ZnVuY3Rpb24tcGFy", "type": "function", "name": "parseGitignore", "qualifiedName": "parseGitignore", "file": "d:\\code\\MCPTEST\\mcptest2\\017_mcp-repl\\src\\js-vector-indexer.js", "startLine": 42, "endLine": 64, "lines": 23, "code": "function parseGitignore(rootDir) {\r\n  const gitignorePath = path.join(rootDir, '.gitignore');\r\n  const patterns = [];\r\n  \r\n  if (existsSync(gitignorePath)) {\r\n    try {\r\n      const content = readFileSync(gitignorePath, 'utf8');\r\n      const lines = content.split('\\n');\r\n      \r\n      for (const line of lines) {\r\n        const trimmedLine = line.trim();\r\n        // Skip empty lines and comments\r\n        if (trimmedLine && !trimmedLine.startsWith('#')) {\r\n          patterns.push(trimmedLine);\r\n        }\r\n      }\r\n    } catch (error) {\r\n      // Silently handle errors\r\n    }\r\n  }\r\n  \r\n  return patterns;\r\n}", "mtime": 1752804097935.2925, "doc": "Parse .gitignore file and get ignore patterns", "isExported": false, "parameters": [{"name": "rootDir", "type": ""}], "returnType": "inferred", "complexity": 4.3, "relationships": {"calls": ["parseGitignore", "join", "existsSync", "readFileSync", "split", "trim", "startsWith", "push"], "dependencies": ["rootDir", "gitignore<PERSON>ath", "path", "gitignore", "patterns", "try", "content", "utf8", "lines", "n", "line", "of", "trimmedLine", "<PERSON><PERSON>", "empty", "and", "comments", "error", "Silently", "handle", "errors"]}, "embedding": null}, {"id": "ZnVuY3Rpb24tc2hv", "type": "function", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "qualifiedName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "file": "d:\\code\\MCPTEST\\mcptest2\\017_mcp-repl\\src\\js-vector-indexer.js", "startLine": 67, "endLine": 101, "lines": 35, "code": "function shouldIgnorePath(filePath, ignorePatterns, rootDir) {\r\n  // Normalize the path relative to the root directory\r\n  const normalizedPath = path.relative(rootDir, filePath);\r\n  // Ensure consistent path separators (use forward slashes for matching)\r\n  const standardPath = normalizedPath.replace(/\\\\/g, '/');\r\n  \r\n  for (const pattern of ignorePatterns) {\r\n    // Handle exact matches\r\n    if (standardPath === pattern || standardPath === pattern.replace(/\\/$/, '')) {\r\n      return true;\r\n    }\r\n    \r\n    // Handle directory wildcards (e.g., dir/**)\r\n    if (pattern.endsWith('/**') && standardPath.startsWith(pattern.slice(0, -2))) {\r\n      return true;\r\n    }\r\n    \r\n    // Handle file wildcards (e.g., *.log)\r\n    if (pattern.startsWith('*.') && standardPath.endsWith(pattern.slice(1))) {\r\n      return true;\r\n    }\r\n    \r\n    // Handle simple directory patterns (e.g., node_modules/)\r\n    if (pattern.endsWith('/') && standardPath.startsWith(pattern)) {\r\n      return true;\r\n    }\r\n    \r\n    // Handle direct file matches\r\n    if (standardPath === pattern) {\r\n      return true;\r\n    }\r\n  }\r\n  \r\n  return false;\r\n}", "mtime": 1752804097935.2925, "doc": "Check if a path should be ignored based on .gitignore patterns", "isExported": false, "parameters": [{"name": "filePath", "type": ""}, {"name": "ignorePatterns", "type": ""}, {"name": "rootDir", "type": ""}], "returnType": "inferred", "complexity": 9.2, "relationships": {"calls": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "relative", "separators", "replace", "wildcards", "endsWith", "startsWith", "slice", "patterns"], "dependencies": ["filePath", "ignorePatterns", "rootDir", "Normalize", "the", "path", "relative", "to", "root", "directory", "normalizedPath", "Ensure", "consistent", "use", "forward", "slashes", "for", "matching", "standardPath", "g", "pattern", "of", "<PERSON><PERSON>", "exact", "matches", "e", "dir", "0", "2", "file", "log", "1", "simple", "node_modules", "direct"]}, "embedding": null}, {"id": "ZnVuY3Rpb24tZ2F0", "type": "function", "name": "gatherFiles", "qualifiedName": "gatherFiles", "file": "d:\\code\\MCPTEST\\mcptest2\\017_mcp-repl\\src\\js-vector-indexer.js", "startLine": 166, "endLine": 199, "lines": 34, "code": "export async function gatherFiles(dir, exts = DEFAULT_EXTS, ignores = DEFAULT_IGNORES) {\r\n  const results = [];\r\n  \r\n  try {\r\n    // Parse .gitignore in the root directory\r\n    const gitignorePatterns = parseGitignore(dir);\r\n    const allIgnores = [...ignores, ...gitignorePatterns];\r\n    \r\n    const entries = await fs.readdir(dir, { withFileTypes: true });\r\n    \r\n    for (const entry of entries) {\r\n      const full = path.join(dir, entry.name);\r\n      \r\n      // Check if the path should be ignored based on standard ignores or .gitignore\r\n      const isIgnoredByPattern = ignores.some(p => full.includes(p));\r\n      const isIgnoredByGitignore = shouldIgnorePath(full, gitignorePatterns, dir);\r\n      \r\n      if (isIgnoredByPattern || isIgnoredByGitignore) {\r\n        continue;\r\n      }\r\n      \r\n      if (entry.isDirectory()) {\r\n        const subDirFiles = await gatherFiles(full, exts, ignores);\r\n        results.push(...subDirFiles);\r\n      } else if (exts.includes(path.extname(entry.name).slice(1))) {\r\n        results.push(full);\r\n      }\r\n    }\r\n  } catch (error) {\r\n    // Silently handle errors\r\n  }\r\n  \r\n  return results;\r\n}", "mtime": 1752804097935.2925, "doc": "Gather files for indexing", "isExported": false, "parameters": [{"name": "dir", "type": ""}, {"name": "exts", "type": ""}, {"name": "ignores", "type": ""}], "returnType": "inferred", "complexity": 7.8, "relationships": {"calls": ["gatherFiles", "parseGitignore", "readdir", "join", "some", "includes", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isDirectory", "push", "extname", "slice"], "dependencies": ["export", "async", "dir", "exts", "DEFAULT_EXTS", "ignores", "DEFAULT_IGNORES", "results", "try", "Parse", "gitignore", "in", "the", "root", "directory", "gitignorePatterns", "allIgnores", "entries", "await", "fs", "entry", "of", "full", "path", "name", "Check", "should", "be", "ignored", "based", "on", "standard", "or", "isIgnoredByPattern", "p", "isIgnoredByGitignore", "continue", "subDirFiles", "1", "error", "Silently", "handle", "errors"]}, "embedding": null}, {"id": "ZnVuY3Rpb24tZXh0", "type": "function", "name": "extractSemanticKeywords", "qualifiedName": "extractSemanticKeywords", "file": "d:\\code\\MCPTEST\\mcptest2\\017_mcp-repl\\src\\js-vector-indexer.js", "startLine": 815, "endLine": 838, "lines": 24, "code": "function extractSemanticKeywords(code) {\r\n  const keywords = new Set();\r\n  \r\n  // Extract variable names, function calls, and meaningful identifiers\r\n  const identifierRegex = /\\b[a-zA-Z_$][a-zA-Z0-9_$]*\\b/g;\r\n  const matches = code.match(identifierRegex) || [];\r\n  \r\n  for (const match of matches) {\r\n    // Skip common keywords and short names\r\n    if (match.length < 3 || isCommonKeyword(match)) continue;\r\n    \r\n    // Split camelCase and snake_case\r\n    const words = match\r\n      .replace(/([a-z])([A-Z])/g, '$1 $2')\r\n      .replace(/_/g, ' ')\r\n      .toLowerCase()\r\n      .split(' ')\r\n      .filter(w => w.length >= 3 && !isCommonKeyword(w));\r\n    \r\n    words.forEach(word => keywords.add(word));\r\n  }\r\n  \r\n  return Array.from(keywords).slice(0, 10); // Limit to most relevant\r\n}", "mtime": 1752804097935.2925, "doc": "Extract semantic keywords from code", "isExported": false, "parameters": [{"name": "code", "type": ""}], "returnType": "inferred", "complexity": 5.9, "relationships": {"calls": ["extractSemanticKeywords", "Set", "match", "isCommonKeyword", "replace", "toLowerCase", "split", "filter", "for<PERSON>ach", "add", "from", "slice"], "dependencies": ["code", "keywords", "new", "Extract", "variable", "names", "calls", "and", "meaningful", "identifiers", "identifierRegex", "b", "a", "zA", "Z_", "Z0", "9_", "g", "matches", "match", "of", "<PERSON><PERSON>", "common", "short", "length", "3", "continue", "Split", "camelCase", "snake_case", "words", "z", "A", "Z", "1", "2", "_", "w", "word", "Array", "0", "10", "Limit", "to", "most", "relevant"]}, "embedding": null}, {"id": "ZnVuY3Rpb24taXNF", "type": "function", "name": "isExported", "qualifiedName": "isExported", "file": "d:\\code\\MCPTEST\\mcptest2\\017_mcp-repl\\src\\js-vector-indexer.js", "startLine": 267, "endLine": 271, "lines": 5, "code": "function isExported(content, position) {\r\n  const linesBefore = content.substring(0, position).split('\\n');\r\n  const currentLine = linesBefore[linesBefore.length - 1];\r\n  return currentLine.includes('export ');\r\n}", "mtime": 1752804097935.2925, "doc": "Extract exported status", "isExported": false, "parameters": [{"name": "content", "type": ""}, {"name": "position", "type": ""}], "returnType": "inferred", "complexity": 3, "relationships": {"calls": ["isExported", "substring", "split", "includes"], "dependencies": ["content", "position", "linesBefore", "0", "n", "currentLine", "length", "1", "export"]}, "embedding": null}, {"id": "ZnVuY3Rpb24tY2Fs", "type": "function", "name": "calculateIntentScore", "qualifiedName": "calculateIntentScore", "file": "d:\\code\\MCPTEST\\mcptest2\\017_mcp-repl\\src\\js-vector-indexer.js", "startLine": 1116, "endLine": 1168, "lines": 53, "code": "function calculateIntentScore(query, chunk) {\r\n  let score = 0;\r\n  const lowerQuery = query.toLowerCase();\r\n  \r\n  // Action intent patterns\r\n  const actionPatterns = {\r\n    'find': ['find', 'search', 'get', 'retrieve', 'fetch'],\r\n    'create': ['create', 'make', 'generate', 'build', 'add'],\r\n    'update': ['update', 'modify', 'change', 'edit', 'set'],\r\n    'delete': ['delete', 'remove', 'destroy', 'clear'],\r\n    'validate': ['validate', 'check', 'verify', 'test'],\r\n    'parse': ['parse', 'process', 'analyze', 'extract'],\r\n    'format': ['format', 'transform', 'convert', 'serialize']\r\n  };\r\n  \r\n  // Type-specific intent matching\r\n  for (const [intent, keywords] of Object.entries(actionPatterns)) {\r\n    if (keywords.some(keyword => lowerQuery.includes(keyword))) {\r\n      // Boost functions that match the intent\r\n      if (chunk.type === 'function' && chunk.name && \r\n          chunk.name.toLowerCase().includes(intent)) {\r\n        score += 0.3;\r\n      }\r\n      \r\n      // Check if code contains related patterns\r\n      const code = chunk.code?.toLowerCase() || '';\r\n      if (keywords.some(keyword => code.includes(keyword))) {\r\n        score += 0.2;\r\n      }\r\n    }\r\n  }\r\n  \r\n  // Domain-specific patterns\r\n  const domainPatterns = {\r\n    'api': ['api', 'endpoint', 'request', 'response', 'http'],\r\n    'data': ['data', 'model', 'schema', 'database', 'sql'],\r\n    'ui': ['ui', 'component', 'render', 'display', 'view'],\r\n    'util': ['util', 'helper', 'utility', 'common', 'shared']\r\n  };\r\n  \r\n  for (const [domain, keywords] of Object.entries(domainPatterns)) {\r\n    if (keywords.some(keyword => lowerQuery.includes(keyword))) {\r\n      const fileName = chunk.file?.toLowerCase() || '';\r\n      const code = chunk.code?.toLowerCase() || '';\r\n      \r\n      if (fileName.includes(domain) || keywords.some(keyword => code.includes(keyword))) {\r\n        score += 0.15;\r\n      }\r\n    }\r\n  }\r\n  \r\n  return score;\r\n}", "mtime": 1752804097935.2925, "doc": "Calculate intent-based scoring for natural language queries", "isExported": false, "parameters": [{"name": "query", "type": ""}, {"name": "chunk", "type": ""}], "returnType": "inferred", "complexity": 10.8, "relationships": {"calls": ["calculateIntentScore", "toLowerCase", "entries", "some", "includes"], "dependencies": ["query", "chunk", "score", "0", "lowerQuery", "Action", "intent", "patterns", "actionPatterns", "find", "search", "get", "retrieve", "fetch", "create", "make", "generate", "build", "add", "update", "modify", "change", "edit", "set", "delete", "remove", "destroy", "clear", "validate", "check", "verify", "test", "parse", "process", "analyze", "extract", "format", "transform", "convert", "serialize", "Type", "specific", "matching", "keywords", "of", "Object", "keyword", "Boost", "functions", "that", "match", "the", "type", "name", "3", "Check", "code", "contains", "related", "2", "Domain", "domainPatterns", "api", "endpoint", "request", "response", "http", "data", "model", "schema", "database", "sql", "ui", "component", "render", "display", "view", "util", "helper", "utility", "common", "shared", "domain", "fileName", "file", "15"]}, "embedding": null}, {"id": "ZnVuY3Rpb24tY3Jl", "type": "function", "name": "createEmbeddingText", "qualifiedName": "createEmbeddingText", "file": "d:\\code\\MCPTEST\\mcptest2\\017_mcp-repl\\src\\js-vector-indexer.js", "startLine": 716, "endLine": 770, "lines": 55, "code": "function createEmbeddingText(chunk) {\r\n  const parts = [];\r\n  \r\n  parts.push(`${chunk.type}: ${chunk.name || chunk.qualifiedName || ''}`);\r\n  \r\n  if (chunk.doc) {\r\n    parts.push(`Documentation: ${chunk.doc}`);\r\n  }\r\n  \r\n  if (chunk.parentClass) {\r\n    parts.push(`In class: ${chunk.parentClass}`);\r\n  }\r\n  \r\n  // Add structural info\r\n  if (chunk.parameters) {\r\n    const paramText = chunk.parameters\r\n      .map(p => p.type ? `${p.name}: ${p.type}` : p.name)\r\n      .join(', ');\r\n    parts.push(`Parameters: ${paramText}`);\r\n  }\r\n  \r\n  if (chunk.returnType) {\r\n    parts.push(`Returns: ${chunk.returnType}`);\r\n  }\r\n  \r\n  if (chunk.complexity) {\r\n    parts.push(`Complexity: ${chunk.complexity}`);\r\n  }\r\n  \r\n  if (chunk.isExported) {\r\n    parts.push('Exported: true');\r\n  }\r\n  \r\n  if (chunk.relationships) {\r\n    if (chunk.relationships.calls && chunk.relationships.calls.length > 0) {\r\n      parts.push(`Calls: ${chunk.relationships.calls.join(', ')}`);\r\n    }\r\n    \r\n    if (chunk.relationships.inheritsFrom) {\r\n      parts.push(`Inherits from: ${chunk.relationships.inheritsFrom}`);\r\n    }\r\n  }\r\n  \r\n  if (chunk.code) {\r\n    // Clean up code to focus on semantics\r\n    const cleanCode = chunk.code\r\n      .replace(/[{};,=()[\\]]/g, ' ')\r\n      .replace(/\\s+/g, ' ')\r\n      .trim();\r\n    \r\n    parts.push(`Code: ${cleanCode}`);\r\n  }\r\n  \r\n  return parts.join(' ');\r\n}", "mtime": 1752804097935.2925, "doc": "Create text representation of a chunk for embedding", "isExported": false, "parameters": [{"name": "chunk", "type": ""}], "returnType": "inferred", "complexity": 8.9, "relationships": {"calls": ["createEmbeddingText", "push", "map", "join", "replace", "trim"], "dependencies": ["chunk", "parts", "type", "name", "qualifiedName", "doc", "parentClass", "In", "Add", "structural", "info", "parameters", "paramText", "p", "returnType", "complexity", "isExported", "relationships", "calls", "length", "0", "inheritsFrom", "Inherits", "code", "Clean", "up", "to", "focus", "on", "semantics", "cleanCode", "g", "s"]}, "embedding": null}, {"id": "ZnVuY3Rpb24tcHJl", "type": "function", "name": "preprocessQuery", "qualifiedName": "preprocessQuery", "file": "d:\\code\\MCPTEST\\mcptest2\\017_mcp-repl\\src\\js-vector-indexer.js", "startLine": 1195, "endLine": 1229, "lines": 35, "code": "function preprocessQuery(query) {\r\n  // Clean and normalize the query\r\n  let processedQuery = query.trim().toLowerCase();\r\n  \r\n  // Handle common natural language patterns\r\n  const patterns = {\r\n    // \"find function that does X\" -> \"X\"\r\n    'find\\\\s+(?:function|method|class)\\\\s+(?:that\\\\s+)?(?:does\\\\s+|for\\\\s+|to\\\\s+)?(.+)': '$1',\r\n    // \"how to X\" -> \"X\"\r\n    'how\\\\s+to\\\\s+(.+)': '$1',\r\n    // \"function for X\" -> \"X\"\r\n    '(?:function|method|class)\\\\s+(?:for|to)\\\\s+(.+)': '$1',\r\n    // \"where is X\" -> \"X\"\r\n    'where\\\\s+is\\\\s+(.+)': '$1',\r\n    // \"what does X do\" -> \"X\"\r\n    'what\\\\s+does\\\\s+(.+?)\\\\s+do': '$1',\r\n    // \"show me X\" -> \"X\"\r\n    'show\\\\s+me\\\\s+(.+)': '$1'\r\n  };\r\n  \r\n  for (const [pattern, replacement] of Object.entries(patterns)) {\r\n    const regex = new RegExp(pattern, 'i');\r\n    const match = processedQuery.match(regex);\r\n    if (match) {\r\n      processedQuery = match[1].trim();\r\n      break;\r\n    }\r\n  }\r\n  \r\n  return {\r\n    original: query,\r\n    processed: processedQuery,\r\n    isNaturalLanguage: query.length > processedQuery.length + 5 // Significant reduction indicates NL\r\n  };\r\n}", "mtime": 1752804097935.2925, "doc": "Enhanced query preprocessing for natural language", "isExported": false, "parameters": [{"name": "query", "type": ""}], "returnType": "inferred", "complexity": 4.5, "relationships": {"calls": ["preprocessQuery", "trim", "toLowerCase", "entries", "RegExp", "match"], "dependencies": ["query", "Clean", "and", "normalize", "the", "processedQuery", "<PERSON><PERSON>", "common", "natural", "language", "patterns", "find", "that", "does", "X", "s", "method", "for", "to", "1", "how", "where", "is", "what", "do", "show", "me", "pattern", "replacement", "of", "Object", "regex", "new", "i", "match", "break", "length", "5", "Significant", "reduction", "indicates", "NL"]}, "embedding": null}, {"id": "ZnVuY3Rpb24tY2xl", "type": "function", "name": "cleanCodeForEmbedding", "qualifiedName": "cleanCodeForEmbedding", "file": "d:\\code\\MCPTEST\\mcptest2\\017_mcp-repl\\src\\js-vector-indexer.js", "startLine": 841, "endLine": 853, "lines": 13, "code": "function cleanCodeForEmbedding(code) {\r\n  return code\r\n    // Remove comments\r\n    .replace(/\\/\\*[\\s\\S]*?\\*\\//g, '')\r\n    .replace(/\\/\\/.*$/gm, '')\r\n    // Remove excessive whitespace\r\n    .replace(/\\s+/g, ' ')\r\n    // Remove common syntax noise\r\n    .replace(/[{}();,]/g, ' ')\r\n    // Trim and limit length\r\n    .trim()\r\n    .substring(0, 200);\r\n}", "mtime": 1752804097935.2925, "doc": "Clean code for better embedding", "isExported": false, "parameters": [{"name": "code", "type": ""}], "returnType": "inferred", "complexity": 1.5, "relationships": {"calls": ["cleanCodeForEmbedding", "replace", "trim", "substring"], "dependencies": ["code", "Remove", "comments", "s", "S", "g", "gm", "excessive", "whitespace", "common", "syntax", "noise", "<PERSON><PERSON>", "and", "limit", "length", "0", "200"]}, "embedding": null}, {"id": "ZnVuY3Rpb24taXND", "type": "function", "name": "isCommonKeyword", "qualifiedName": "isCommonKeyword", "file": "d:\\code\\MCPTEST\\mcptest2\\017_mcp-repl\\src\\js-vector-indexer.js", "startLine": 856, "endLine": 867, "lines": 12, "code": "function isCommonKeyword(word) {\r\n  const commonWords = new Set([\r\n    'const', 'let', 'var', 'function', 'class', 'if', 'else', 'for', 'while',\r\n    'return', 'import', 'export', 'from', 'async', 'await', 'try', 'catch',\r\n    'throw', 'new', 'this', 'super', 'extends', 'implements', 'interface',\r\n    'type', 'enum', 'public', 'private', 'protected', 'static', 'readonly',\r\n    'true', 'false', 'null', 'undefined', 'void', 'any', 'string', 'number',\r\n    'boolean', 'object', 'array', 'map', 'set', 'date', 'error', 'promise'\r\n  ]);\r\n  \r\n  return commonWords.has(word.toLowerCase());\r\n}", "mtime": 1752804097935.2925, "doc": "Check if word is a common programming keyword", "isExported": false, "parameters": [{"name": "word", "type": ""}], "returnType": "inferred", "complexity": 5.5, "relationships": {"calls": ["isCommonKeyword", "Set", "has", "toLowerCase"], "dependencies": ["word", "commonWords", "new", "for", "while", "import", "export", "from", "async", "await", "try", "catch", "throw", "extends", "implements", "interface", "type", "enum", "public", "private", "protected", "static", "readonly", "void", "any", "string", "number", "boolean", "object", "array", "map", "set", "date", "error", "promise"]}, "embedding": null}, {"id": "ZnVuY3Rpb24tZ2Vu", "type": "function", "name": "generateNameVariants", "qualifiedName": "generateNameVariants", "file": "d:\\code\\MCPTEST\\mcptest2\\017_mcp-repl\\src\\js-vector-indexer.js", "startLine": 1068, "endLine": 1086, "lines": 19, "code": "function generateNameVariants(name) {\r\n  if (!name) return [''];\r\n  \r\n  const variants = [name.toLowerCase()];\r\n  \r\n  // Split camelCase\r\n  const camelSplit = name.replace(/([a-z])([A-Z])/g, '$1 $2').toLowerCase();\r\n  variants.push(camelSplit);\r\n  \r\n  // Split snake_case\r\n  const snakeSplit = name.replace(/_/g, ' ').toLowerCase();\r\n  variants.push(snakeSplit);\r\n  \r\n  // Split kebab-case\r\n  const kebabSplit = name.replace(/-/g, ' ').toLowerCase();\r\n  variants.push(kebabSplit);\r\n  \r\n  return [...new Set(variants)];\r\n}", "mtime": 1752804097935.2925, "doc": "Generate name variants for better matching", "isExported": false, "parameters": [{"name": "name", "type": ""}], "returnType": "inferred", "complexity": 3.5, "relationships": {"calls": ["generateNameVariants", "toLowerCase", "replace", "push", "Set"], "dependencies": ["name", "variants", "Split", "camelCase", "camelSplit", "a", "z", "A", "Z", "g", "1", "2", "snake_case", "snakeSplit", "_", "kebab", "case", "kebabSplit", "new"]}, "embedding": null}, {"id": "ZnVuY3Rpb24tc2F2", "type": "function", "name": "saveIndex", "qualifiedName": "saveIndex", "file": "d:\\code\\MCPTEST\\mcptest2\\017_mcp-repl\\src\\js-vector-indexer.js", "startLine": 890, "endLine": 900, "lines": 11, "code": "async function saveIndex(indexDir = INDEX_DIR) {\r\n  try {\r\n    // Save code chunks\r\n    const indexPath = path.join(indexDir, INDEX_FILE);\r\n    await fs.writeFile(indexPath, JSON.stringify(codeChunks));\r\n    \r\n    return true;\r\n  } catch (error) {\r\n    return false;\r\n  }\r\n}", "mtime": 1752804097935.2925, "doc": "Save index to disk", "isExported": false, "parameters": [{"name": "indexDir", "type": ""}], "returnType": "inferred", "complexity": 3.5, "relationships": {"calls": ["saveIndex", "join", "writeFile", "stringify"], "dependencies": ["async", "indexDir", "INDEX_DIR", "try", "Save", "code", "chunks", "indexPath", "path", "INDEX_FILE", "await", "fs", "JSON", "codeChunks", "error"]}, "embedding": null}, {"id": "ZnVuY3Rpb24tc3lu", "type": "function", "name": "syncIndex", "qualifiedName": "syncIndex", "file": "d:\\code\\MCPTEST\\mcptest2\\017_mcp-repl\\src\\js-vector-indexer.js", "startLine": 903, "endLine": 982, "lines": 80, "code": "export async function syncIndex(folders, exts = DEFAULT_EXTS, ignores = DEFAULT_IGNORES) {\r\n  if (!isInitialized) {\r\n    await initialize();\r\n  }\r\n  \r\n  // Gather all files\r\n  const files = [];\r\n  for (const folder of folders) {\r\n    const folderFiles = await gatherFiles(folder, exts, ignores);\r\n    files.push(...folderFiles);\r\n  }\r\n  \r\n  // Process files and extract chunks\r\n  let newChunksCount = 0;\r\n  const allNewChunks = [];\r\n  const updatedChunkIds = new Set();\r\n  \r\n  for (const file of files) {\r\n    try {\r\n      const fileChunks = await extractChunks(file);\r\n      \r\n      for (const chunk of fileChunks) {\r\n        updatedChunkIds.add(chunk.id);\r\n        \r\n        // Check if chunk exists with the same mtime\r\n        const existingIndex = chunkIds.indexOf(chunk.id);\r\n        if (existingIndex !== -1 && codeChunks[existingIndex].mtime === chunk.mtime) {\r\n          continue;\r\n        }\r\n        \r\n        // Generate embedding for the chunk\r\n        const text = createEmbeddingText(chunk);\r\n        chunk.embedding = await generateEmbedding(text, chunk);\r\n        \r\n        allNewChunks.push(chunk);\r\n        newChunksCount++;\r\n      }\r\n    } catch (error) {\r\n      // Silently handle errors\r\n    }\r\n  }\r\n  \r\n  // Find chunks to delete (chunks not in updated files)\r\n  const chunksToDelete = codeChunks.filter(chunk => !updatedChunkIds.has(chunk.id));\r\n  \r\n  // Update the in-memory index\r\n  if (allNewChunks.length > 0 || chunksToDelete.length > 0) {\r\n    // Remove deleted chunks\r\n    for (const chunk of chunksToDelete) {\r\n      const index = chunkIds.indexOf(chunk.id);\r\n      if (index !== -1) {\r\n        // Remove from code chunks array\r\n        codeChunks.splice(index, 1);\r\n        chunkIds.splice(index, 1);\r\n      }\r\n    }\r\n    \r\n    // Add new chunks\r\n    for (const chunk of allNewChunks) {\r\n      const existingIndex = chunkIds.indexOf(chunk.id);\r\n      if (existingIndex !== -1) {\r\n        // Update existing chunk\r\n        codeChunks[existingIndex] = chunk;\r\n      } else {\r\n        // Add new chunk\r\n        codeChunks.push(chunk);\r\n        chunkIds.push(chunk.id);\r\n      }\r\n    }\r\n    \r\n    // Save the updated index\r\n    await saveIndex();\r\n  }\r\n  \r\n  return { \r\n    total: codeChunks.length, \r\n    new: newChunksCount,\r\n    deleted: chunksToDelete.length\r\n  };\r\n}", "mtime": 1752804097935.2925, "doc": "Synchronize the index with the file system", "isExported": false, "parameters": [{"name": "folders", "type": ""}, {"name": "exts", "type": ""}, {"name": "ignores", "type": ""}], "returnType": "inferred", "complexity": 9.6, "relationships": {"calls": ["syncIndex", "initialize", "gatherFiles", "push", "Set", "extractChunks", "add", "indexOf", "createEmbeddingText", "generateEmbedding", "delete", "filter", "has", "splice", "saveIndex"], "dependencies": ["export", "async", "folders", "exts", "DEFAULT_EXTS", "ignores", "DEFAULT_IGNORES", "isInitialized", "await", "<PERSON><PERSON>", "all", "files", "folder", "of", "folderFiles", "Process", "and", "extract", "chunks", "newChunksCount", "0", "allNewChunks", "updatedChunkIds", "new", "file", "try", "fileChunks", "chunk", "id", "Check", "exists", "with", "the", "same", "mtime", "existingIndex", "chunkIds", "1", "codeChunks", "continue", "Generate", "embedding", "for", "text", "error", "Silently", "handle", "errors", "Find", "to", "not", "in", "updated", "chunksToDelete", "Update", "memory", "index", "length", "Remove", "deleted", "from", "code", "array", "Add", "existing", "Save"]}, "embedding": null}, {"id": "ZnVuY3Rpb24tdGV4", "type": "function", "name": "textMatchScore", "qualifiedName": "textMatchScore", "file": "d:\\code\\MCPTEST\\mcptest2\\017_mcp-repl\\src\\js-vector-indexer.js", "startLine": 985, "endLine": 1042, "lines": 58, "code": "function textMatchScore(query, chunk) {\r\n  // Normalize query and text\r\n  const normalizedQuery = query.toLowerCase();\r\n  const normalizedCode = chunk.code ? chunk.code.toLowerCase() : '';\r\n  const normalizedName = chunk.name ? chunk.name.toLowerCase() : '';\r\n  const normalizedQualifiedName = chunk.qualifiedName ? chunk.qualifiedName.toLowerCase() : '';\r\n  const normalizedDoc = chunk.doc ? chunk.doc.toLowerCase() : '';\r\n  \r\n  let score = 0;\r\n  \r\n  // Enhanced exact matching with camelCase and snake_case handling\r\n  const queryVariants = generateQueryVariants(query);\r\n  const nameVariants = generateNameVariants(chunk.name || '');\r\n  \r\n  // Exact matches (highest priority)\r\n  for (const queryVariant of queryVariants) {\r\n    for (const nameVariant of nameVariants) {\r\n      if (nameVariant === queryVariant) {\r\n        score += 2.0;\r\n        break;\r\n      }\r\n    }\r\n  }\r\n  \r\n  // Partial name matches with intelligent scoring\r\n  if (score === 0) {\r\n    for (const queryVariant of queryVariants) {\r\n      if (normalizedName.includes(queryVariant) || normalizedQualifiedName.includes(queryVariant)) {\r\n        // Score based on match quality and position\r\n        const matchQuality = queryVariant.length / Math.max(normalizedName.length, normalizedQualifiedName.length);\r\n        const isAtStart = normalizedName.startsWith(queryVariant) || normalizedQualifiedName.startsWith(queryVariant);\r\n        score += (isAtStart ? 1.2 : 0.8) * matchQuality;\r\n      }\r\n    }\r\n  }\r\n  \r\n  // Documentation and comments scoring (for natural language)\r\n  if (normalizedDoc.includes(normalizedQuery)) {\r\n    const docWords = normalizedDoc.split(/\\s+/);\r\n    const queryWords = normalizedQuery.split(/\\s+/);\r\n    const matchRatio = queryWords.filter(word => docWords.some(docWord => docWord.includes(word))).length / queryWords.length;\r\n    score += 0.8 * matchRatio;\r\n  }\r\n  \r\n  // Code context scoring with semantic understanding\r\n  const codeScore = calculateCodeContextScore(normalizedQuery, normalizedCode, chunk);\r\n  score += codeScore;\r\n  \r\n  // Natural language intent scoring\r\n  const intentScore = calculateIntentScore(query, chunk);\r\n  score += intentScore;\r\n  \r\n  // Type-specific boosting\r\n  const typeBoost = getTypeBoost(query, chunk.type);\r\n  score *= typeBoost;\r\n  \r\n  return Math.min(score, 2.0); // Allow higher scores for better matches\r\n}", "mtime": 1752804097935.2925, "doc": "Enhanced natural language code search scoring", "isExported": false, "parameters": [{"name": "query", "type": ""}, {"name": "chunk", "type": ""}], "returnType": "inferred", "complexity": 10.9, "relationships": {"calls": ["textMatchScore", "toLowerCase", "generateQueryVariants", "generateNameVariants", "matches", "includes", "max", "startsWith", "scoring", "split", "filter", "some", "calculateCodeContextScore", "calculateIntentScore", "getTypeBoost", "min"], "dependencies": ["query", "chunk", "Normalize", "and", "text", "normalizedQuery", "normalizedCode", "code", "normalizedName", "name", "normalizedQualifiedName", "qualifiedName", "normalizedDoc", "doc", "score", "0", "Enhanced", "exact", "matching", "with", "camelCase", "snake_case", "handling", "queryVariants", "nameVariants", "Exact", "highest", "priority", "query<PERSON><PERSON>t", "of", "nameVariant", "2", "break", "Partial", "matches", "intelligent", "scoring", "Score", "based", "on", "match", "quality", "position", "matchQuality", "length", "Math", "isAtStart", "1", "8", "Documentation", "comments", "for", "natural", "language", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "s", "queryWords", "matchRatio", "word", "<PERSON><PERSON><PERSON><PERSON>", "Code", "context", "semantic", "understanding", "codeScore", "Natural", "intent", "intentScore", "Type", "specific", "boosting", "typeBoost", "type", "Allow", "higher", "scores", "better"]}, "embedding": null}, {"id": "ZnVuY3Rpb24tZ2V0", "type": "function", "name": "getTypeBoost", "qualifiedName": "getTypeBoost", "file": "d:\\code\\MCPTEST\\mcptest2\\017_mcp-repl\\src\\js-vector-indexer.js", "startLine": 1171, "endLine": 1192, "lines": 22, "code": "function getTypeBoost(query, type) {\r\n  const lowerQuery = query.toLowerCase();\r\n  \r\n  // Query suggests looking for specific types\r\n  if (lowerQuery.includes('function') && type === 'function') return 1.3;\r\n  if (lowerQuery.includes('class') && type === 'class') return 1.3;\r\n  if (lowerQuery.includes('method') && type === 'method') return 1.3;\r\n  if (lowerQuery.includes('variable') && type === 'property') return 1.2;\r\n  if (lowerQuery.includes('file') && type === 'file') return 1.2;\r\n  \r\n  // Default boosts by type importance\r\n  switch (type) {\r\n    case 'function': return 1.1; // Functions are often what users search for\r\n    case 'class': return 1.05;\r\n    case 'method': return 1.0;\r\n    case 'export': return 0.95;\r\n    case 'property': return 0.9;\r\n    case 'import': return 0.8;\r\n    case 'file': return 0.7; // Files are less likely to be direct search targets\r\n    default: return 1.0;\r\n  }\r\n}", "mtime": 1752804097935.2925, "doc": "Get type-specific boost for relevance", "isExported": false, "parameters": [{"name": "query", "type": ""}, {"name": "type", "type": ""}], "returnType": "inferred", "complexity": 17, "relationships": {"calls": ["getTypeBoost", "toLowerCase", "includes"], "dependencies": ["query", "type", "lowerQuery", "Query", "suggests", "looking", "for", "specific", "types", "1", "3", "method", "variable", "property", "2", "file", "<PERSON><PERSON><PERSON>", "boosts", "by", "importance", "case", "Functions", "are", "often", "what", "users", "search", "05", "0", "export", "95", "9", "import", "8", "7", "Files", "less", "likely", "to", "be", "direct", "targets"]}, "embedding": null}, {"id": "ZnVuY3Rpb24tcXVl", "type": "function", "name": "queryIndex", "qualifiedName": "queryIndex", "file": "d:\\code\\MCPTEST\\mcptest2\\017_mcp-repl\\src\\js-vector-indexer.js", "startLine": 1232, "endLine": 1410, "lines": 179, "code": "export async function queryIndex(query, topK = 8) {\r\n  if (!isInitialized) {\r\n    await initialize();\r\n  }\r\n  \r\n  try {\r\n    if (codeChunks.length === 0) {\r\n      return [];\r\n    }\r\n    \r\n    // Preprocess the query for better understanding\r\n    const queryInfo = preprocessQuery(query);\r\n    const searchQuery = queryInfo.processed;\r\n    \r\n    // Generate embeddings for both original and processed queries\r\n    const [originalEmbedding, processedEmbedding] = await Promise.all([\r\n      generateEmbedding(query),\r\n      searchQuery !== query ? generateEmbedding(searchQuery) : null\r\n    ]);\r\n    \r\n    let scoredResults = [];\r\n    \r\n    // Enhanced scoring with multiple query variants\r\n    scoredResults = codeChunks.map(chunk => {\r\n      let bestScore = 0;\r\n      \r\n      // Try original query\r\n      if (originalEmbedding && chunk.embedding) {\r\n        const vectorScore = cosineSimilarity(originalEmbedding, chunk.embedding);\r\n        const textScore = textMatchScore(query, chunk);\r\n        const combinedScore = (vectorScore * 0.6) + (textScore * 0.4);\r\n        bestScore = Math.max(bestScore, combinedScore);\r\n      }\r\n      \r\n      // Try processed query if different\r\n      if (processedEmbedding && chunk.embedding && searchQuery !== query) {\r\n        const vectorScore = cosineSimilarity(processedEmbedding, chunk.embedding);\r\n        const textScore = textMatchScore(searchQuery, chunk);\r\n        const combinedScore = (vectorScore * 0.6) + (textScore * 0.4);\r\n        bestScore = Math.max(bestScore, combinedScore);\r\n      }\r\n      \r\n      // Fallback to text-only scoring\r\n      if (bestScore === 0) {\r\n        bestScore = Math.max(\r\n          textMatchScore(query, chunk),\r\n          textMatchScore(searchQuery, chunk)\r\n        );\r\n      }\r\n      \r\n      // Apply natural language boost\r\n      if (queryInfo.isNaturalLanguage) {\r\n        bestScore *= 1.1; // Slight boost for NL queries to surface more relevant results\r\n      }\r\n      \r\n      return {\r\n        score: bestScore,\r\n        chunk\r\n      };\r\n    });\r\n    \r\n    // Filter out zero scores and sort by score descending\r\n    const filteredResults = scoredResults\r\n      .filter(result => result.score > 0)\r\n      .sort((a, b) => b.score - a.score)\r\n      .slice(0, topK);\r\n    \r\n    // Format results for display\r\n    const results = filteredResults.map(result => {\r\n      const chunk = result.chunk;\r\n      \r\n      // Base result structure\r\n      const formattedResult = {\r\n        score: parseFloat(result.score.toFixed(3)),\r\n        file: chunk.file,\r\n        startLine: chunk.startLine + 1,\r\n        endLine: chunk.endLine + 1,\r\n        type: chunk.type,\r\n        name: chunk.name || '',\r\n        qualifiedName: chunk.qualifiedName || '',\r\n        lines: chunk.lines,\r\n        doc: chunk.doc || '',\r\n        code: chunk.code ? (chunk.code.length > 140 ? \r\n          chunk.code.replace(/\\s+/g, ' ').slice(0, 140) + '...' : \r\n          chunk.code.replace(/\\s+/g, ' ')\r\n        ) : ''\r\n      };\r\n      \r\n      // Add structural metadata based on chunk type\r\n      switch (chunk.type) {\r\n        case 'file':\r\n          formattedResult.structure = {\r\n            childCount: chunk.children ? chunk.children.length : 0,\r\n            exportCount: chunk.exports ? chunk.exports.length : 0\r\n          };\r\n          break;\r\n          \r\n        case 'function':\r\n          formattedResult.structure = {\r\n            isExported: chunk.isExported || false,\r\n            complexity: chunk.complexity || 1,\r\n            parameters: chunk.parameters || [],\r\n            returnType: chunk.returnType || '',\r\n            calls: chunk.relationships?.calls || []\r\n          };\r\n          break;\r\n          \r\n        case 'class':\r\n          formattedResult.structure = {\r\n            isExported: chunk.isExported || false,\r\n            methodCount: chunk.methods ? chunk.methods.length : 0,\r\n            propertyCount: chunk.properties ? chunk.properties.length : 0,\r\n            parentClass: chunk.parentClass || null,\r\n            inheritsFrom: chunk.relationships?.inheritsFrom || null\r\n          };\r\n          break;\r\n          \r\n        case 'method':\r\n          formattedResult.structure = {\r\n            parentClass: chunk.parentClass || '',\r\n            isStatic: chunk.isStatic || false,\r\n            complexity: chunk.complexity || 1,\r\n            parameters: chunk.parameters || [],\r\n            returnType: chunk.returnType || '',\r\n            calls: chunk.relationships?.calls || []\r\n          };\r\n          break;\r\n          \r\n        case 'property':\r\n          formattedResult.structure = {\r\n            parentClass: chunk.parentClass || '',\r\n            isStatic: chunk.isStatic || false,\r\n            propertyType: chunk.propertyType || ''\r\n          };\r\n          break;\r\n          \r\n        case 'import':\r\n          formattedResult.structure = {\r\n            modulePath: chunk.modulePath || '',\r\n            importedElements: chunk.elements || []\r\n          };\r\n          break;\r\n          \r\n        case 'export':\r\n          formattedResult.structure = {\r\n            exportedElements: chunk.elements || []\r\n          };\r\n          break;\r\n      }\r\n      \r\n      // Add relationships data if available\r\n      if (chunk.relationships) {\r\n        formattedResult.relationships = {};\r\n        \r\n        if (chunk.relationships.calls && chunk.relationships.calls.length > 0) {\r\n          formattedResult.relationships.calls = chunk.relationships.calls;\r\n        }\r\n        \r\n        if (chunk.relationships.dependencies && chunk.relationships.dependencies.length > 0) {\r\n          formattedResult.relationships.dependencies = chunk.relationships.dependencies;\r\n        }\r\n        \r\n        if (chunk.relationships.inheritsFrom) {\r\n          formattedResult.relationships.inheritsFrom = chunk.relationships.inheritsFrom;\r\n        }\r\n        \r\n        if (chunk.relationships.dependsOn) {\r\n          formattedResult.relationships.dependsOn = chunk.relationships.dependsOn;\r\n        }\r\n      }\r\n      \r\n      return formattedResult;\r\n    });\r\n    \r\n    return results;\r\n  } catch (error) {\r\n    return [];\r\n  }\r\n}", "mtime": 1752804097935.2925, "doc": "Query the index with enhanced natural language support", "isExported": false, "parameters": [{"name": "query", "type": ""}, {"name": "topK", "type": ""}], "returnType": "inferred", "complexity": 35.9, "relationships": {"calls": ["queryIndex", "initialize", "preprocessQuery", "all", "generateEmbedding", "map", "cosineSimilarity", "textMatchScore", "max", "filter", "sort", "slice", "parseFloat", "toFixed", "replace"], "dependencies": ["export", "async", "query", "topK", "8", "isInitialized", "await", "try", "codeChunks", "length", "0", "Preprocess", "the", "for", "better", "understanding", "queryInfo", "searchQuery", "processed", "Generate", "embeddings", "both", "original", "and", "queries", "originalEmbedding", "processedEmbedding", "Promise", "scoredResults", "Enhanced", "scoring", "with", "multiple", "variants", "chunk", "bestScore", "Try", "embedding", "vectorScore", "textScore", "combinedScore", "6", "4", "Math", "different", "Fallback", "to", "text", "only", "Apply", "natural", "language", "boost", "isNaturalLanguage", "1", "Slight", "NL", "surface", "more", "relevant", "results", "Filter", "out", "zero", "scores", "sort", "by", "score", "descending", "filteredResults", "result", "a", "b", "Format", "display", "Base", "structure", "formattedResult", "3", "file", "startLine", "endLine", "type", "name", "qualifiedName", "lines", "doc", "code", "140", "s", "g", "Add", "structural", "metadata", "based", "on", "case", "children", "exports", "break", "isExported", "complexity", "parameters", "returnType", "relationships", "calls", "methods", "properties", "parentClass", "inheritsFrom", "method", "isStatic", "property", "propertyType", "import", "modulePath", "elements", "data", "available", "dependencies", "dependsOn", "error"]}, "embedding": null}, {"id": "aW1wb3J0LTMxMi1k", "type": "import", "qualifiedName": "import fs from 'fs/promises';", "file": "d:\\code\\MCPTEST\\mcptest2\\017_mcp-repl\\src\\js-vector-indexer.js", "startLine": 8, "endLine": 8, "lines": 1, "code": "import fs from 'fs/promises';", "mtime": 1752804097935.2925, "doc": "", "modulePath": "fs/promises", "elements": ["fs (default)"], "relationships": {"dependsOn": "fs/promises", "imports": ["fs (default)"]}, "embedding": null}, {"id": "aW1wb3J0LTM0My1k", "type": "import", "qualifiedName": "import { existsSync, readFileSync, writeFileSync, mkdirSync } from 'fs';", "file": "d:\\code\\MCPTEST\\mcptest2\\017_mcp-repl\\src\\js-vector-indexer.js", "startLine": 9, "endLine": 9, "lines": 1, "code": "import { existsSync, readFileSync, writeFileSync, mkdirSync } from 'fs';", "mtime": 1752804097935.2925, "doc": "", "modulePath": "fs", "elements": ["existsSync", "readFileSync", "writeFileSync", "mkdirSync"], "relationships": {"dependsOn": "fs", "imports": ["existsSync", "readFileSync", "writeFileSync", "mkdirSync"]}, "embedding": null}, {"id": "aW1wb3J0LTQxNy1k", "type": "import", "qualifiedName": "import path from 'path';", "file": "d:\\code\\MCPTEST\\mcptest2\\017_mcp-repl\\src\\js-vector-indexer.js", "startLine": 10, "endLine": 10, "lines": 1, "code": "import path from 'path';", "mtime": 1752804097935.2925, "doc": "", "modulePath": "path", "elements": ["path (default)"], "relationships": {"dependsOn": "path", "imports": ["path (default)"]}, "embedding": null}, {"id": "aW1wb3J0LTQ0My1k", "type": "import", "qualifiedName": "import { pipeline, env } from '@xenova/transformers';", "file": "d:\\code\\MCPTEST\\mcptest2\\017_mcp-repl\\src\\js-vector-indexer.js", "startLine": 11, "endLine": 11, "lines": 1, "code": "import { pipeline, env } from '@xenova/transformers';", "mtime": 1752804097935.2925, "doc": "", "modulePath": "@xenova/transformers", "elements": ["pipeline", "env"], "relationships": {"dependsOn": "@xenova/transformers", "imports": ["pipeline", "env"]}, "embedding": null}, {"id": "ZXhwb3J0LTM0MTct", "type": "export", "qualifiedName": "export async function initialize(indexDir = INDEX_DIR) {\r\n  if (isInitialized) return true;", "file": "d:\\code\\MCPTEST\\mcptest2\\017_mcp-repl\\src\\js-vector-indexer.js", "startLine": 104, "endLine": 105, "lines": 2, "code": "export async function initialize(indexDir = INDEX_DIR) {\r\n  if (isInitialized) return true;", "mtime": 1752804097935.2925, "doc": "", "modulePath": "", "elements": [], "embedding": null}, {"id": "ZXhwb3J0LTQ5MTkt", "type": "export", "qualifiedName": "export async function gatherFiles(dir, exts = DEFAULT_EXTS, ignores = DEFAULT_IGNORES) {\r\n  const results = [];", "file": "d:\\code\\MCPTEST\\mcptest2\\017_mcp-repl\\src\\js-vector-indexer.js", "startLine": 166, "endLine": 167, "lines": 2, "code": "export async function gatherFiles(dir, exts = DEFAULT_EXTS, ignores = DEFAULT_IGNORES) {\r\n  const results = [];", "mtime": 1752804097935.2925, "doc": "", "modulePath": "", "elements": [], "embedding": null}, {"id": "ZXhwb3J0LTgxODQt", "type": "export", "qualifiedName": "exported status\r\nfunction isExported(content, position) {\r\n  const linesBefore = content.substring(0, position).split('\\n');", "file": "d:\\code\\MCPTEST\\mcptest2\\017_mcp-repl\\src\\js-vector-indexer.js", "startLine": 266, "endLine": 268, "lines": 3, "code": "exported status\r\nfunction isExported(content, position) {\r\n  const linesBefore = content.substring(0, position).split('\\n');", "mtime": 1752804097935.2925, "doc": "", "modulePath": "", "elements": [], "embedding": null}, {"id": "ZXhwb3J0LTg0MDEt", "type": "export", "qualifiedName": "export ');", "file": "d:\\code\\MCPTEST\\mcptest2\\017_mcp-repl\\src\\js-vector-indexer.js", "startLine": 270, "endLine": 270, "lines": 1, "code": "export ');", "mtime": 1752804097935.2925, "doc": "", "modulePath": "", "elements": [], "embedding": null}, {"id": "ZXhwb3J0LTg0NTct", "type": "export", "qualifiedName": "export async function extractChunks(filePath) {\r\n  try {\r\n    const content = await fs.readFile(filePath, 'utf-8');", "file": "d:\\code\\MCPTEST\\mcptest2\\017_mcp-repl\\src\\js-vector-indexer.js", "startLine": 274, "endLine": 276, "lines": 3, "code": "export async function extractChunks(filePath) {\r\n  try {\r\n    const content = await fs.readFile(filePath, 'utf-8');", "mtime": 1752804097935.2925, "doc": "", "modulePath": "", "elements": [], "embedding": null}, {"id": "ZXhwb3J0LTg5MTct", "type": "export", "qualifiedName": "exports: []\r\n    };", "file": "d:\\code\\MCPTEST\\mcptest2\\017_mcp-repl\\src\\js-vector-indexer.js", "startLine": 286, "endLine": 287, "lines": 2, "code": "exports: []\r\n    };", "mtime": 1752804097935.2925, "doc": "", "modulePath": "", "elements": [], "embedding": null}, {"id": "ZXhwb3J0LTkwOTQt", "type": "export", "qualifiedName": "export\\s+)?(?:async\\s+)?function\\s+(\\w+)\\s*\\([^)]*\\)\\s*(?::\\s*[^{]+)?\\s*{/g;", "file": "d:\\code\\MCPTEST\\mcptest2\\017_mcp-repl\\src\\js-vector-indexer.js", "startLine": 293, "endLine": 293, "lines": 1, "code": "export\\s+)?(?:async\\s+)?function\\s+(\\w+)\\s*\\([^)]*\\)\\s*(?::\\s*[^{]+)?\\s*{/g;", "mtime": 1752804097935.2925, "doc": "", "modulePath": "", "elements": [], "embedding": null}, {"id": "ZXhwb3J0LTEwOTMx", "type": "export", "qualifiedName": "exports.push(funcChunk.id);", "file": "d:\\code\\MCPTEST\\mcptest2\\017_mcp-repl\\src\\js-vector-indexer.js", "startLine": 344, "endLine": 344, "lines": 1, "code": "exports.push(funcChunk.id);", "mtime": 1752804097935.2925, "doc": "", "modulePath": "", "elements": [], "embedding": null}, {"id": "ZXhwb3J0LTExMjI0", "type": "export", "qualifiedName": "export\\s+)?class\\s+(\\w+)(?:\\s+extends\\s+(\\w+))?\\s*{/g;", "file": "d:\\code\\MCPTEST\\mcptest2\\017_mcp-repl\\src\\js-vector-indexer.js", "startLine": 355, "endLine": 355, "lines": 1, "code": "export\\s+)?class\\s+(\\w+)(?:\\s+extends\\s+(\\w+))?\\s*{/g;", "mtime": 1752804097935.2925, "doc": "", "modulePath": "", "elements": [], "embedding": null}, {"id": "ZXhwb3J0LTEyOTcz", "type": "export", "qualifiedName": "exports.push(classChunk.id);", "file": "d:\\code\\MCPTEST\\mcptest2\\017_mcp-repl\\src\\js-vector-indexer.js", "startLine": 405, "endLine": 405, "lines": 1, "code": "exports.push(classChunk.id);", "mtime": 1752804097935.2925, "doc": "", "modulePath": "", "elements": [], "embedding": null}, {"id": "aW1wb3J0LTE4MTAz", "type": "import", "qualifiedName": "imports/exports\r\n    const importExportRegex = /(import|export)[\\s\\S]+?;", "file": "d:\\code\\MCPTEST\\mcptest2\\017_mcp-repl\\src\\js-vector-indexer.js", "startLine": 543, "endLine": 544, "lines": 2, "code": "imports/exports\r\n    const importExportRegex = /(import|export)[\\s\\S]+?;", "mtime": 1752804097935.2925, "doc": "", "modulePath": "", "elements": [], "relationships": {"dependsOn": "", "imports": []}, "embedding": null}, {"id": "aW1wb3J0LTE4MTg4", "type": "import", "qualifiedName": "importMatch;", "file": "d:\\code\\MCPTEST\\mcptest2\\017_mcp-repl\\src\\js-vector-indexer.js", "startLine": 545, "endLine": 545, "lines": 1, "code": "importMatch;", "mtime": 1752804097935.2925, "doc": "", "modulePath": "", "elements": [], "relationships": {"dependsOn": "", "imports": []}, "embedding": null}, {"id": "aW1wb3J0LTE4MjIw", "type": "import", "qualifiedName": "importMatch = importExportRegex.exec(content)) !== null) {\r\n      const code = importMatch[0];", "file": "d:\\code\\MCPTEST\\mcptest2\\017_mcp-repl\\src\\js-vector-indexer.js", "startLine": 547, "endLine": 548, "lines": 2, "code": "importMatch = importExportRegex.exec(content)) !== null) {\r\n      const code = importMatch[0];", "mtime": 1752804097935.2925, "doc": "", "modulePath": "", "elements": [], "relationships": {"dependsOn": "", "imports": []}, "embedding": null}, {"id": "aW1wb3J0LTE4MzM1", "type": "import", "qualifiedName": "importMatch[1] === 'import' ? 'import' : 'export';", "file": "d:\\code\\MCPTEST\\mcptest2\\017_mcp-repl\\src\\js-vector-indexer.js", "startLine": 549, "endLine": 549, "lines": 1, "code": "importMatch[1] === 'import' ? 'import' : 'export';", "mtime": 1752804097935.2925, "doc": "", "modulePath": "", "elements": [], "relationships": {"dependsOn": "", "imports": []}, "embedding": null}, {"id": "aW1wb3J0LTE4NDc3", "type": "import", "qualifiedName": "importMatch.index).split('\\n').length - 1;", "file": "d:\\code\\MCPTEST\\mcptest2\\017_mcp-repl\\src\\js-vector-indexer.js", "startLine": 551, "endLine": 551, "lines": 1, "code": "importMatch.index).split('\\n').length - 1;", "mtime": 1752804097935.2925, "doc": "", "modulePath": "", "elements": [], "relationships": {"dependsOn": "", "imports": []}, "embedding": null}, {"id": "aW1wb3J0LTE4NTkw", "type": "import", "qualifiedName": "imported/exported elements and module\r\n      let modulePath = '';", "file": "d:\\code\\MCPTEST\\mcptest2\\017_mcp-repl\\src\\js-vector-indexer.js", "startLine": 554, "endLine": 555, "lines": 2, "code": "imported/exported elements and module\r\n      let modulePath = '';", "mtime": 1752804097935.2925, "doc": "", "modulePath": "", "elements": [], "relationships": {"dependsOn": "", "imports": []}, "embedding": null}, {"id": "aW1wb3J0LTE4NzEx", "type": "import", "qualifiedName": "import') {\r\n        const moduleMatch = code.match(/from\\s+['\"]([^'\"]+)['\"]/);", "file": "d:\\code\\MCPTEST\\mcptest2\\017_mcp-repl\\src\\js-vector-indexer.js", "startLine": 558, "endLine": 559, "lines": 2, "code": "import') {\r\n        const moduleMatch = code.match(/from\\s+['\"]([^'\"]+)['\"]/);", "mtime": 1752804097935.2925, "doc": "", "modulePath": "", "elements": [], "relationships": {"dependsOn": "", "imports": []}, "embedding": null}, {"id": "aW1wb3J0LTE5MDgx", "type": "import", "qualifiedName": "import\r\n          const defaultMatch = code.match(/import\\s+(\\w+)/);", "file": "d:\\code\\MCPTEST\\mcptest2\\017_mcp-repl\\src\\js-vector-indexer.js", "startLine": 568, "endLine": 569, "lines": 2, "code": "import\r\n          const defaultMatch = code.match(/import\\s+(\\w+)/);", "mtime": 1752804097935.2925, "doc": "", "modulePath": "", "elements": ["const (default)"], "relationships": {"dependsOn": "", "imports": ["const (default)"]}, "embedding": null}, {"id": "ZXhwb3J0LTE5NTE3", "type": "export", "qualifiedName": "export\\s+default\\s+(\\w+)/);", "file": "d:\\code\\MCPTEST\\mcptest2\\017_mcp-repl\\src\\js-vector-indexer.js", "startLine": 580, "endLine": 580, "lines": 1, "code": "export\\s+default\\s+(\\w+)/);", "mtime": 1752804097935.2925, "doc": "", "modulePath": "", "elements": [], "embedding": null}, {"id": "aW1wb3J0LTE5NzM2", "type": "import", "qualifiedName": "importMatch.index}-${filePath}`).toString('base64').replace(/[^a-zA-Z0-9]/g, '').substring(0, 16),\r\n        type,\r\n        qualifiedName: code.trim(),\r\n        file: filePath,\r\n        startLine: startPos,\r\n        endLine: endPos,\r\n        lines,\r\n        code,\r\n        mtime: stat.mtimeMs,\r\n        doc: '',\r\n        modulePath,\r\n        elements\r\n      };", "file": "d:\\code\\MCPTEST\\mcptest2\\017_mcp-repl\\src\\js-vector-indexer.js", "startLine": 588, "endLine": 600, "lines": 13, "code": "importMatch.index}-${filePath}`).toString('base64').replace(/[^a-zA-Z0-9]/g, '').substring(0, 16),\r\n        type,\r\n        qualifiedName: code.trim(),\r\n        file: filePath,\r\n        startLine: startPos,\r\n        endLine: endPos,\r\n        lines,\r\n        code,\r\n        mtime: stat.mtimeMs,\r\n        doc: '',\r\n        modulePath,\r\n        elements\r\n      };", "mtime": 1752804097935.2925, "doc": "", "modulePath": "", "elements": ["filePath"], "relationships": {"dependsOn": "", "imports": ["filePath"]}, "embedding": null}, {"id": "aW1wb3J0LTIwMTYw", "type": "import", "qualifiedName": "import') {\r\n        // Add dependency relationships\r\n        relationships.set(chunk.id, {\r\n          dependsOn: modulePath,\r\n          imports: elements\r\n        });", "file": "d:\\code\\MCPTEST\\mcptest2\\017_mcp-repl\\src\\js-vector-indexer.js", "startLine": 604, "endLine": 609, "lines": 6, "code": "import') {\r\n        // Add dependency relationships\r\n        relationships.set(chunk.id, {\r\n          dependsOn: modulePath,\r\n          imports: elements\r\n        });", "mtime": 1752804097935.2925, "doc": "", "modulePath": "", "elements": ["// Add dependency relationships\r\n        relationships.set(chunk.id", "{\r\n          dependsOn: modulePath", "imports: elements"], "relationships": {"dependsOn": "", "imports": ["// Add dependency relationships\r\n        relationships.set(chunk.id", "{\r\n          dependsOn: modulePath", "imports: elements"]}, "embedding": null}, {"id": "ZXhwb3J0LTIwODQ1", "type": "export", "qualifiedName": "exports: fileScope.exports\r\n    };", "file": "d:\\code\\MCPTEST\\mcptest2\\017_mcp-repl\\src\\js-vector-indexer.js", "startLine": 627, "endLine": 628, "lines": 2, "code": "exports: fileScope.exports\r\n    };", "mtime": 1752804097935.2925, "doc": "", "modulePath": "", "elements": [], "embedding": null}, {"id": "aW1wb3J0LTI3NTY0", "type": "import", "qualifiedName": "import', 'export', 'from', 'async', 'await', 'try', 'catch',\r\n    'throw', 'new', 'this', 'super', 'extends', 'implements', 'interface',\r\n    'type', 'enum', 'public', 'private', 'protected', 'static', 'readonly',\r\n    'true', 'false', 'null', 'undefined', 'void', 'any', 'string', 'number',\r\n    'boolean', 'object', 'array', 'map', 'set', 'date', 'error', 'promise'\r\n  ]);", "file": "d:\\code\\MCPTEST\\mcptest2\\017_mcp-repl\\src\\js-vector-indexer.js", "startLine": 859, "endLine": 864, "lines": 6, "code": "import', 'export', 'from', 'async', 'await', 'try', 'catch',\r\n    'throw', 'new', 'this', 'super', 'extends', 'implements', 'interface',\r\n    'type', 'enum', 'public', 'private', 'protected', 'static', 'readonly',\r\n    'true', 'false', 'null', 'undefined', 'void', 'any', 'string', 'number',\r\n    'boolean', 'object', 'array', 'map', 'set', 'date', 'error', 'promise'\r\n  ]);", "mtime": 1752804097935.2925, "doc": "", "modulePath": "", "elements": [], "relationships": {"dependsOn": "", "imports": []}, "embedding": null}, {"id": "ZXhwb3J0LTI4Nzk1", "type": "export", "qualifiedName": "export async function syncIndex(folders, exts = DEFAULT_EXTS, ignores = DEFAULT_IGNORES) {\r\n  if (!isInitialized) {\r\n    await initialize();", "file": "d:\\code\\MCPTEST\\mcptest2\\017_mcp-repl\\src\\js-vector-indexer.js", "startLine": 903, "endLine": 905, "lines": 3, "code": "export async function syncIndex(folders, exts = DEFAULT_EXTS, ignores = DEFAULT_IGNORES) {\r\n  if (!isInitialized) {\r\n    await initialize();", "mtime": 1752804097935.2925, "doc": "", "modulePath": "", "elements": [], "embedding": null}, {"id": "aW1wb3J0LTM4MTk1", "type": "import", "qualifiedName": "importance\r\n  switch (type) {\r\n    case 'function': return 1.1;", "file": "d:\\code\\MCPTEST\\mcptest2\\017_mcp-repl\\src\\js-vector-indexer.js", "startLine": 1181, "endLine": 1183, "lines": 3, "code": "importance\r\n  switch (type) {\r\n    case 'function': return 1.1;", "mtime": 1752804097935.2925, "doc": "", "modulePath": "", "elements": [], "relationships": {"dependsOn": "", "imports": []}, "embedding": null}, {"id": "ZXhwb3J0LTM4Mzc5", "type": "export", "qualifiedName": "export': return 0.95;", "file": "d:\\code\\MCPTEST\\mcptest2\\017_mcp-repl\\src\\js-vector-indexer.js", "startLine": 1186, "endLine": 1186, "lines": 1, "code": "export': return 0.95;", "mtime": 1752804097935.2925, "doc": "", "modulePath": "", "elements": [], "embedding": null}, {"id": "aW1wb3J0LTM4NDQ2", "type": "import", "qualifiedName": "import': return 0.8;", "file": "d:\\code\\MCPTEST\\mcptest2\\017_mcp-repl\\src\\js-vector-indexer.js", "startLine": 1188, "endLine": 1188, "lines": 1, "code": "import': return 0.8;", "mtime": 1752804097935.2925, "doc": "", "modulePath": "", "elements": [], "relationships": {"dependsOn": "", "imports": []}, "embedding": null}, {"id": "ZXhwb3J0LTM5ODIy", "type": "export", "qualifiedName": "export async function queryIndex(query, topK = 8) {\r\n  if (!isInitialized) {\r\n    await initialize();", "file": "d:\\code\\MCPTEST\\mcptest2\\017_mcp-repl\\src\\js-vector-indexer.js", "startLine": 1232, "endLine": 1234, "lines": 3, "code": "export async function queryIndex(query, topK = 8) {\r\n  if (!isInitialized) {\r\n    await initialize();", "mtime": 1752804097935.2925, "doc": "", "modulePath": "", "elements": [], "embedding": null}, {"id": "ZXhwb3J0LTQzMDMw", "type": "export", "qualifiedName": "exportCount: chunk.exports ? chunk.exports.length : 0\r\n          };", "file": "d:\\code\\MCPTEST\\mcptest2\\017_mcp-repl\\src\\js-vector-indexer.js", "startLine": 1325, "endLine": 1326, "lines": 2, "code": "exportCount: chunk.exports ? chunk.exports.length : 0\r\n          };", "mtime": 1752804097935.2925, "doc": "", "modulePath": "", "elements": [], "embedding": null}, {"id": "aW1wb3J0LTQ0NjAw", "type": "import", "qualifiedName": "import':\r\n          formattedResult.structure = {\r\n            modulePath: chunk.modulePath || '',\r\n            importedElements: chunk.elements || []\r\n          };", "file": "d:\\code\\MCPTEST\\mcptest2\\017_mcp-repl\\src\\js-vector-indexer.js", "startLine": 1368, "endLine": 1372, "lines": 5, "code": "import':\r\n          formattedResult.structure = {\r\n            modulePath: chunk.modulePath || '',\r\n            importedElements: chunk.elements || []\r\n          };", "mtime": 1752804097935.2925, "doc": "", "modulePath": "", "elements": ["modulePath: chunk.modulePath || ''", "importedElements: chunk.elements || []"], "relationships": {"dependsOn": "", "imports": ["modulePath: chunk.modulePath || ''", "importedElements: chunk.elements || []"]}, "embedding": null}, {"id": "ZXhwb3J0LTQ0ODEw", "type": "export", "qualifiedName": "export':\r\n          formattedResult.structure = {\r\n            exportedElements: chunk.elements || []\r\n          };", "file": "d:\\code\\MCPTEST\\mcptest2\\017_mcp-repl\\src\\js-vector-indexer.js", "startLine": 1375, "endLine": 1378, "lines": 4, "code": "export':\r\n          formattedResult.structure = {\r\n            exportedElements: chunk.elements || []\r\n          };", "mtime": 1752804097935.2925, "doc": "", "modulePath": "", "elements": ["exportedElements: chunk.elements || []"], "embedding": null}, {"id": "ZnVuY3Rpb24tdGVz", "type": "function", "name": "testMCPRepl", "qualifiedName": "testMCPRepl", "file": "d:\\code\\MCPTEST\\mcptest2\\017_mcp-repl\\test-mcp-repl.js", "startLine": 5, "endLine": 72, "lines": 68, "code": "async function testMCPRepl() {\n    console.log('🧪 测试MCP REPL服务器...');\n    \n    try {\n        // 创建客户端传输\n        const transport = new StdioClientTransport({\n            command: 'node',\n            args: ['src/direct-executor-server.js']\n        });\n\n        // 创建客户端\n        const client = new Client({\n            name: \"test-repl-client\",\n            version: \"1.0.0\"\n        }, {\n            capabilities: {}\n        });\n\n        // 连接到服务器\n        console.log('🔗 正在连接到MCP REPL服务器...');\n        await client.connect(transport);\n        console.log('✅ 连接成功！');\n\n        // 列出可用工具\n        console.log('🛠️  正在获取可用工具...');\n        const tools = await client.listTools();\n        console.log(`✅ 发现 ${tools.tools.length} 个工具:`);\n        \n        tools.tools.forEach((tool, index) => {\n            console.log(`  ${index + 1}. ${tool.name}`);\n            console.log(`     描述: ${tool.description}`);\n        });\n\n        // 测试一个简单的代码执行（如果有相关工具）\n        const executeTools = tools.tools.filter(tool => \n            tool.name.includes('execute') || \n            tool.name.includes('run') || \n            tool.name.includes('eval')\n        );\n\n        if (executeTools.length > 0) {\n            console.log('\\n🚀 测试代码执行功能...');\n            try {\n                const result = await client.callTool({\n                    name: executeTools[0].name,\n                    arguments: { \n                        code: 'console.log(\"Hello from MCP REPL!\");',\n                        language: 'javascript'\n                    }\n                });\n                console.log('✅ 代码执行成功！');\n                console.log('📄 执行结果:', result.content);\n            } catch (error) {\n                console.log('⚠️  代码执行测试失败:', error.message);\n            }\n        }\n\n        // 关闭连接\n        await client.close();\n        console.log('✅ 测试完成，连接已关闭');\n\n        return true;\n\n    } catch (error) {\n        console.error('❌ MCP REPL测试失败:', error.message);\n        return false;\n    }\n}", "mtime": 1752897305113.0022, "doc": "", "isExported": false, "parameters": [], "returnType": "inferred", "complexity": 5.6, "relationships": {"calls": ["testMCPRepl", "log", "StdioClientTransport", "Client", "connect", "listTools", "for<PERSON>ach", "filter", "includes", "callTool", "close", "error"], "dependencies": ["async", "console", "MCP", "REPL", "try", "transport", "new", "node", "src", "direct", "executor", "server", "js", "client", "test", "repl", "1", "0", "await", "tools", "length", "tool", "index", "name", "description", "executeTools", "execute", "run", "eval", "n", "result", "Hello", "from", "javascript", "content", "error", "message"]}, "embedding": null}, {"id": "aW1wb3J0LTIxLWQ6", "type": "import", "qualifiedName": "import { Client } from '@modelcontextprotocol/sdk/client/index.js';", "file": "d:\\code\\MCPTEST\\mcptest2\\017_mcp-repl\\test-mcp-repl.js", "startLine": 2, "endLine": 2, "lines": 1, "code": "import { Client } from '@modelcontextprotocol/sdk/client/index.js';", "mtime": 1752897305113.0022, "doc": "", "modulePath": "@modelcontextprotocol/sdk/client/index.js", "elements": ["Client"], "relationships": {"dependsOn": "@modelcontextprotocol/sdk/client/index.js", "imports": ["Client"]}, "embedding": null}, {"id": "aW1wb3J0LTg5LWQ6", "type": "import", "qualifiedName": "import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';", "file": "d:\\code\\MCPTEST\\mcptest2\\017_mcp-repl\\test-mcp-repl.js", "startLine": 3, "endLine": 3, "lines": 1, "code": "import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';", "mtime": 1752897305113.0022, "doc": "", "modulePath": "@modelcontextprotocol/sdk/client/stdio.js", "elements": ["StdioClientTransport"], "relationships": {"dependsOn": "@modelcontextprotocol/sdk/client/stdio.js", "imports": ["StdioClientTransport"]}, "embedding": null}]