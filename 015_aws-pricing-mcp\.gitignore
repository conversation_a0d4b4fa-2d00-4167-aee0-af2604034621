# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
venv/
env/
ENV/
.env

# IDE files
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# Docker
.dockerignore
docker-compose.override.yml

# Logs
*.log
logs/

# Cache
.cache/
.pytest_cache/
.coverage
htmlcov/

# Data
*.csv
ec2_pricing.json

# Jupyter Notebooks
.ipynb_checkpoints

.aws-sam
samconfig.toml
