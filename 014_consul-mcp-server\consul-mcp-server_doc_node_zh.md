## 1. 前置准备

### 先决条件
- Node.js 18.0 或更高版本
- npm 包管理器
- Git
- Consul 服务器

安装所需软件：
```bash
# 安装 Node.js
# 访问 https://nodejs.org 下载并安装最新版本

# 安装 Git
# 访问 https://git-scm.com 下载并安装

# 克隆项目并安装依赖
git clone https://github.com/kocierik/consul-mcp-server.git
cd consul-mcp-server
npm install
npm run build
```

## 2. 配置环境变量

在 MCP NOW 客户端中设置以下环境变量：

**配置说明：**
- `CONSUL_HOST`: Consul 服务器主机地址（默认：localhost）
- `CONSUL_PORT`: Consul 服务器端口（默认：8500）

## 3. 启动服务

完成以上步骤后，点击下方的 **安装** 按钮连接并启动服务。

启动命令：
```bash
node build/index.js
```
