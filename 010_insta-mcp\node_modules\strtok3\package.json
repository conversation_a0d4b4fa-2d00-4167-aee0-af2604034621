{"name": "strtok3", "version": "10.2.2", "description": "A promise based streaming tokenizer", "author": {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Borewit"}, "funding": {"type": "github", "url": "https://github.com/sponsors/Borewit"}, "scripts": {"clean": "del-cli 'lib/**/*.js' 'lib/**/*.js.map' 'lib/**/*.d.ts' 'test/**/*.js' 'test/**/*.js.map'", "compile-src": "tsc -p lib", "compile-test": "tsc -p test", "compile": "yarn run compile-src && yarn run compile-test", "build": "yarn run clean && yarn run compile", "eslint": "eslint lib test", "lint-md": "remark -u preset-lint-recommended .", "lint-ts": "biome check", "lint": "yarn run lint-md && yarn run lint-ts", "fix": "yarn run biome lint --write", "test": "mocha", "bun:test": "bun run --bun test", "test-coverage": "c8 yarn run test", "send-codacy": "c8 report --reporter=text-lcov | codacy-coverage", "start": "yarn run compile && yarn run lint && yarn run cover-test"}, "engines": {"node": ">=18"}, "repository": {"type": "git", "url": "https://github.com/Borewit/strtok3.git"}, "license": "MIT", "type": "module", "exports": {".": {"node": "./lib/index.js", "default": "./lib/core.js"}, "./core": "./lib/core.js"}, "types": "lib/index.d.ts", "files": ["lib/**/*.js", "lib/**/*.d.ts"], "bugs": {"url": "https://github.com/Borewit/strtok3/issues"}, "dependencies": {"@tokenizer/token": "^0.3.0", "peek-readable": "^7.0.0"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "@types/chai": "^5.0.1", "@types/chai-as-promised": "^8.0.1", "@types/debug": "^4.1.12", "@types/mocha": "^10.0.10", "@types/node": "^22.13.8", "c8": "^10.1.3", "chai": "^5.2.0", "chai-as-promised": "^8.0.1", "del-cli": "^6.0.0", "mocha": "^11.1.0", "node-readable-to-web-readable-stream": "^0.4.2", "remark-cli": "^12.0.1", "remark-preset-lint-recommended": "^7.0.1", "token-types": "^6.0.0", "ts-node": "^10.9.2", "typescript": "^5.8.2", "uint8array-extras": "^1.4.0"}, "keywords": ["tokenizer", "reader", "token", "async", "promise", "parser", "decoder", "binary", "endian", "uint", "stream", "streaming"], "packageManager": "yarn@4.6.0"}