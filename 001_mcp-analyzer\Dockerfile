# Generated by https://smithery.ai. See: https://smithery.ai/docs/config#dockerfile
# Use Node LTS on Alpine
FROM node:lts-alpine AS builder

# Set working directory
WORKDIR /app

# Copy dependency definitions
COPY package.json package-lock.json tsconfig.json ./
# Copy source
COPY src ./src
COPY assets ./assets

# Install dependencies (skip prepare scripts), then build
RUN npm ci --ignore-scripts
RUN npm run build

# Final image
FROM node:lts-alpine AS runtime
WORKDIR /app

# Copy built files and assets
COPY --from=builder /app/build ./build
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/assets ./assets

# Expose any needed port (none by default)

# Entry point
ENTRYPOINT ["node", "build/index.js"]
