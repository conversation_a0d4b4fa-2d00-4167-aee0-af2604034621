UML MCP Server - NPX 启动步骤

1. 安装Node.js和npm
- 访问 https://nodejs.org/ 下载并安装Node.js 18.0+
- npm通常随Node.js一起安装
- 验证安装：`node --version` 和 `npm --version`

2. 验证Smithery CLI工具
- 运行 `npx @smithery/cli --version` 确认工具可用
- Smithery CLI将自动下载

3. 验证网络连接
- 确保能够访问npm registry
- 测试基本的网络连通性
- 检查防火墙设置

4. 使用Smithery安装UML MCP服务
- 运行 `npx -y @smithery/cli install @Swayingleaves/uml-mcp-server --client claude`
- Smithery将自动配置和安装服务

5. 验证服务运行
- 确认服务正常启动
- 通过MCP客户端测试UML生成功能

6. 测试UML功能
- 测试UML图生成
- 验证图像输出功能

