## 1. Setup & Requirements

### Prerequisites
- Node.js 18.0 or higher
- npm package manager
- Git
- Microsoft Azure app registration

Install required software:
```bash
# Install Node.js
# Visit https://nodejs.org to download and install the latest version

# Install Git
# Visit https://git-scm.com to download and install

# Clone project and install dependencies
git clone https://github.com/anoopt/outlook-meetings-scheduler-mcp-server.git
cd outlook-meetings-scheduler-mcp-server
npm install
npm run build
```

Setup Microsoft Azure app:
1. Register a new app in Azure Portal
2. Create a client secret
3. Grant necessary permissions (Microsoft Graph API > Application permissions > Calendars.ReadWrite, People.Read.All, User.ReadBasic.All)
4. Note your Client ID, Client Secret, and Tenant ID

## 2. Configure Environment Variables

Configure the following environment variables in MCP NOW client:

**Configuration Details:**
- `CLIENT_ID`: Azure app Client ID
- `CLIENT_SECRET`: Azure app Client Secret
- `TENANT_ID`: Azure Tenant ID
- `USER_EMAIL`: User email address

## 3. Launch the Server

After completing the above steps, click the **Install** button below to connect and start the service.

Launch command:
```bash
node build/index.js
```
