Tesla MCP - Node 启动步骤

1. 安装Node.js和npm
- 访问 https://nodejs.org/ 下载并安装Node.js 18.0+
- npm会随Node.js自动安装
- 安装Ngrok：访问 https://ngrok.com/download 下载并安装
- 验证安装：`node --version`、`npm --version` 和 `ngrok --version`

2. 克隆源码并进入目录
- 运行 `git clone https://github.com/scald/tesla-mcp.git`
- 进入项目目录：`cd tesla-mcp`

3. 安装依赖并构建
- 运行 `npm install` 安装项目依赖
- 运行 `npm run build` 构建项目
- 等待构建完成
- 确认build目录生成成功

4. 获取Tesla API凭据
- 访问Tesla Developer Portal (https://developer.tesla.com/)
- 创建应用并获取Client ID和Client Secret
- 运行 `npm run get-token` 获取refresh token
- 运行 `npm run register` 注册应用

5. 设置环境变量
- 设置 `TESLA_CLIENT_ID` 为Tesla API Client ID
- 设置 `TESLA_CLIENT_SECRET` 为Tesla API Client Secret
- 设置 `TESLA_REFRESH_TOKEN` 为Tesla API Refresh Token

6. 启动服务
- 运行 `node build/index.js` 启动服务

7. 验证服务运行
- 确认服务正常启动
- 通过MCP客户端测试Tesla车辆控制功能
