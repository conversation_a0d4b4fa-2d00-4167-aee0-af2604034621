## 1. Setup & Requirements

### Prerequisites
- Node.js 18.0 or higher
- Atlassian account with access to Confluence Cloud

Install Node.js:
```bash
# Visit https://nodejs.org to download and install the latest version
# Or use package managers, for example:
# Windows: winget install OpenJS.NodeJS
# macOS: brew install node
# Ubuntu: sudo apt install nodejs npm
```

Get Atlassian API Token:
1. Go to https://id.atlassian.com/manage-profile/security/api-tokens
2. Click **Create API token**
3. Enter a descriptive label (e.g., mcp-confluence-access)
4. Click **Create**
5. Copy the generated API token (shown only once)

## 2. Configure Environment Variables

Configure the following environment variables in MCP NOW client:

**Configuration Details:**
- `ATLASSIAN_SITE_NAME`: Confluence site name (e.g., mycompany for mycompany.atlassian.net)
- `ATLASSIAN_USER_EMAIL`: Atlassian account email
- `ATLASSIAN_API_TOKEN`: API token obtained from step 1

## 3. Launch the Server

After completing the above steps, click the **Install** button below to connect and start the service.

Launch command:
```bash
npx -y @aashari/mcp-server-atlassian-confluence
```
