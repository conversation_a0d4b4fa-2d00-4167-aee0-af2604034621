## 1. Setup & Requirements

### Prerequisites
- Node.js 18.0 or higher
- npm package manager

Install Node.js and global package:
```bash
# Install Node.js
# Visit https://nodejs.org to download and install the latest version

# Install mcp-maven-deps globally
npm install -g mcp-maven-deps
```

## 2. Configure Environment Variables

No additional configuration required.

## 3. Launch the Server

After completing the above steps, click the **Install** button below to connect and start the service.

Launch command:
```bash
mcp-maven-deps
```
