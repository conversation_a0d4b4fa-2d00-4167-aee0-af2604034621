Consul MCP Server - Node 启动步骤

1. 安装Node.js和Consul
- 访问 https://nodejs.org/ 下载并安装Node.js 18.0+
- 访问 https://consul.io/ 下载并安装Consul服务器
- npm通常随Node.js一起安装
- 验证安装：`node --version` 和 `consul --version`

2. 启动Consul服务器
- 运行 `consul agent -dev` 启动开发模式Consul
- 确认Consul在端口8500运行
- 访问 http://localhost:8500 验证Consul UI

3. 克隆源码并进入目录
- 运行 `git clone https://github.com/kocierik/consul-mcp-server.git`
- 进入项目目录：`cd consul-mcp-server`

4. 安装依赖并构建项目
- 运行 `npm install` 安装项目依赖
- 运行 `npm run build` 构建项目
- 确认build目录生成成功

5. 设置环境变量
- 设置 `CONSUL_HOST` 为Consul主机地址（默认：localhost）
- 设置 `CONSUL_PORT` 为Consul端口（默认：8500）

6. 启动Consul MCP服务
- 运行 `node build/index.js`
- 确保使用构建后文件的路径

7. 验证服务运行
- 确认服务正常启动
- 通过MCP客户端测试Consul管理功能
