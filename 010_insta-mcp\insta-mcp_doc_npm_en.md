## 1. Setup & Requirements

### Prerequisites
- Node.js 18.0 or higher
- npm package manager
- Python 3.8 or higher
- Instagram account credentials (Session ID, CSRF Token, DS User ID)

Install required software:
```bash
# Install Node.js
# Visit https://nodejs.org to download and install the latest version

# Install Python
# Visit https://python.org to download and install

# Install package globally
npm install -g instagram-dm-mcp

# Run setup script to install Python dependencies
instagram-dm-mcp-setup
```

Get Instagram credentials:
1. Log into Instagram on Chrome
2. Right-click on the page and select "Inspect"
3. Go to the "Application" tab
4. Click on "Cookies" in the left sidebar
5. Copy the values for `sessionid`, `csrftoken`, and `ds_user_id`

## 2. Configure Environment Variables

Configure the following environment variables in MCP NOW client:

**Configuration Details:**
- `INSTAGRAM_SESSION_ID`: Instagram Session ID
- `INSTAGRAM_CSRF_TOKEN`: Instagram CSRF Token
- `INSTAGRAM_DS_USER_ID`: Instagram DS User ID

## 3. Launch the Server

After completing the above steps, click the **Install** button below to connect and start the service.

Launch command:
```bash
npx -y instagram-dm-mcp start
```
