## 1. Setup & Requirements

### Prerequisites
- Python 3.8 or higher
- pip package manager
- Git

Install required software:
```bash
# Install Python 3.8+
# Visit https://python.org to download and install

# Install Git
# Visit https://git-scm.com to download and install

# Clone project and install dependencies
git clone https://github.com/trilogy-group/aws-pricing-mcp.git
cd aws-pricing-mcp
pip install -r requirements.txt
```

## 2. Configure Environment Variables

No additional configuration required.

## 3. Launch the Server

After completing the above steps, click the **Install** button below to connect and start the service.

Launch command:
```bash
python src/server.py
```
