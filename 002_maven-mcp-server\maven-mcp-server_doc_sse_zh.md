## 1. 前置准备

### 先决条件
- Node.js 18.0 或更高版本
- npm 包管理器

安装 Node.js：
```bash
# 访问 https://nodejs.org 下载并安装最新版本
# 或使用包管理器安装，例如：
# Windows: winget install OpenJS.NodeJS
# macOS: brew install node
# Ubuntu: sudo apt install nodejs npm
```

## 2. 配置环境变量

不需要额外配置

## 3. 启动服务

完成以上步骤后，点击下方的 **安装** 按钮连接并启动服务。

启动命令（本地访问）：
```bash
npx mcp-maven-deps --port=3000
```

启动命令（远程访问）：
```bash
npx mcp-maven-deps --host=0.0.0.0 --port=3000
```
