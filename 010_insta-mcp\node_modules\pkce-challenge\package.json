{"name": "pkce-challenge", "version": "4.1.0", "description": "Generate or verify a Proof Key for Code Exchange (PKCE) challenge pair", "source": "src/index.ts", "main": "dist/index.node.js", "browser": "dist/index.browser.js", "type": "module", "types": "dist/index.node.d.ts", "files": ["dist/"], "scripts": {"watch": "tsc --watch --declaration", "preprocess": "env=browser diverge -f src/index.ts src/index.browser.ts && env=node diverge -f src/index.ts src/index.node.ts", "build": "npm run preprocess && tsc --declaration", "test": "node --experimental-vm-modules node_modules/jest/bin/jest.js", "test:bundle": "esbuild browser-test/index.js --bundle --outfile=browser-test/index.out.js"}, "repository": {"type": "git", "url": "git+https://github.com/crouchcd/pkce-challenge.git"}, "keywords": ["PKCE", "oauth2"], "author": "crouchcd", "license": "MIT", "bugs": {"url": "https://github.com/crouchcd/pkce-challenge/issues"}, "homepage": "https://github.com/crouchcd/pkce-challenge#readme", "engines": {"node": ">=16.20.0"}, "devDependencies": {"@types/jest": "^29.5.0", "@types/node": "^18.15.11", "diverge": "^1.0.2", "esbuild": "^0.19.11", "jest": "^29.5.0", "typescript": "^5.0.3"}}