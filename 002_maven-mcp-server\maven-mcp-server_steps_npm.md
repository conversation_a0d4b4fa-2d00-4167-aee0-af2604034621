Maven MCP Server - NPM 全局安装启动步骤

1. 安装Node.js和npm
- 访问 https://nodejs.org/ 下载并安装Node.js 18.0+
- npm通常随Node.js一起安装
- 验证安装：`node --version` 和 `npm --version`

2. 全局安装mcp-maven-deps
- 运行 `npm install -g mcp-maven-deps`
- 等待安装完成
- 验证安装：`mcp-maven-deps --version`

3. 验证全局安装
- 运行 `npm list -g mcp-maven-deps` 确认安装成功
- 检查全局包路径

4. 启动Maven MCP服务
- 运行 `mcp-maven-deps`
- 服务将直接启动

5. 验证服务运行
- 确认服务正常启动
- 通过MCP客户端测试Maven依赖管理功能

6. 测试功能
- 测试Maven项目分析
- 验证依赖解析功能

