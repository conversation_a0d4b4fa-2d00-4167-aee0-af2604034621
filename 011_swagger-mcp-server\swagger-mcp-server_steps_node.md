Swagger MCP Server - Node 启动步骤

1. 安装Node.js和包管理器
- 访问 https://nodejs.org/ 下载并安装Node.js 18.0+
- npm通常随Node.js一起安装
- 可选安装pnpm：`npm install -g pnpm`
- 验证安装：`node --version` 和 `npm --version`

2. 克隆源码并进入目录
- 运行 `git clone https://github.com/tuskermanshu/swagger-mcp-server.git`
- 进入项目目录：`cd swagger-mcp-server`

3. 安装依赖
- 运行 `npm install` 或 `pnpm install`
- 等待安装完成
- 确认依赖安装成功

4. 验证项目结构
- 检查 `start-server.js` 文件是否存在
- 确认项目配置正确

5. 启动Swagger MCP服务
- 运行 `node start-server.js`
- 服务将直接启动

6. 验证服务运行
- 确认服务正常启动
- 通过MCP客户端测试Swagger功能
