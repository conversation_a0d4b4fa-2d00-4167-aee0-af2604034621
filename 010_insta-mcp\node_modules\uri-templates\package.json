{"name": "uri-templates", "version": "0.2.0", "description": "URI Templates (RFC6570) including de-substitution", "main": "uri-templates.js", "scripts": {"test": "mocha --bail"}, "repository": {"type": "git", "url": "https://github.com/geraintluff/uri-templates.git"}, "keywords": ["rfc6570", "uri", "template"], "author": "<PERSON><PERSON><PERSON>", "license": "http://geraintluff.github.io/tv4/LICENSE.txt", "readmeFilename": "README.md", "gitHead": "3c65c2e1809e8c0dca1fb6b440c18fbc7acd85da", "devDependencies": {"grunt": "~0.4.1", "grunt-contrib-clean": "~0.4.1", "grunt-contrib-jshint": "~0.6.0", "mocha": "~1.11.0", "grunt-mocha": "~0.3.4", "grunt-mocha-test": "~0.5.0", "grunt-cli": "~0.1.9", "grunt-contrib-uglify": "~0.2.2", "grunt-contrib-copy": "~0.4.1", "proclaim": "1.4", "mocha-unfunk-reporter": "~0.2", "jshint-path-reporter": "~0.1", "grunt-concat-sourcemap": "~0.2", "source-map-support": "~0.1", "grunt-markdown": "~0.3.0"}}