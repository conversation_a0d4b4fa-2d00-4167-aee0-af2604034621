Render MCP - Node 启动步骤

1. 安装Node.js和npm
- 访问 https://nodejs.org/ 下载并安装Node.js 18.0+
- npm通常随Node.js一起安装
- 验证安装：`node --version` 和 `npm --version`

2. 克隆源码并进入目录
- 运行 `git clone https://github.com/niyogi/render-mcp.git`
- 进入项目目录：`cd render-mcp`

3. 安装依赖
- 运行 `npm install` 安装项目依赖
- 等待安装完成

4. 获取Render API密钥
- 访问 Render Dashboard (https://dashboard.render.com/account/api-keys)
- 创建新的API密钥
- 复制生成的API密钥

5. 设置环境变量
- 设置 `RENDER_API_KEY` 为获取的API密钥
- 确认环境变量设置正确

6. 启动Render MCP服务
- 运行 `node bin/render-mcp.js start`
- 确保使用正确的文件路径

7. 验证服务运行
- 确认服务正常启动
- 通过MCP客户端测试Render功能
