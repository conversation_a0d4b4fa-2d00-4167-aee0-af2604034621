[project]
name = "brave-search-mcp"
version = "0.1.0"
description = "A Model Context Protocol (MCP) server for Brave Search"
authors = [
    {name = "Your Name", email = "<EMAIL>"},
]
dependencies = [
    "mcp",
    "httpx",
    "fastmcp",
]
requires-python = ">=3.11"
readme = "README.md"
license = {text = "MIT"}

[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[tool.setuptools]
packages = ["src"]

[project.optional-dependencies]
dev = [
    "pytest",
    "black",
    "isort",
]

[tool.black]
line-length = 88
target-version = ['py37']

[tool.isort]
profile = "black"
line_length = 88

[tool.pytest.ini_options]
testpaths = ["tests"]
