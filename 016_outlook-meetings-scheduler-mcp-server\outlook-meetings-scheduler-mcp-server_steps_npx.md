Outlook Meetings Scheduler MCP Server - NPX 启动步骤

1. 安装Node.js和npm
- 访问 https://nodejs.org/ 下载并安装Node.js 18.0+
- npm通常随Node.js一起安装
- 验证安装：`node --version` 和 `npm --version`

2. 设置Microsoft Azure应用
- 在Azure Portal中注册新应用
- 创建客户端密钥
- 授予必要权限（Microsoft Graph API > Application permissions > Calendars.ReadWrite, People.Read.All, User.ReadBasic.All）
- 记录Client ID、Client Secret和Tenant ID

3. 设置环境变量
- 设置 `CLIENT_ID` 为Azure应用的Client ID
- 设置 `CLIENT_SECRET` 为Azure应用的Client Secret
- 设置 `TENANT_ID` 为Azure租户ID
- 设置 `USER_EMAIL` 为用户邮箱地址

4. 使用NPX启动Outlook MCP服务
- 运行 `npx outlook-meetings-scheduler@latest`

