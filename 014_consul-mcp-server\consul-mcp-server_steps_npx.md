Consul MCP Server - NPX 启动步骤

1. 安装Node.js和Consul
- 访问 https://nodejs.org/ 下载并安装Node.js 18.0+
- 访问 https://consul.io/ 下载并安装Consul服务器
- npm通常随Node.js一起安装
- 验证安装：`node --version` 和 `consul --version`

2. 启动Consul服务器
- 运行 `consul agent -dev` 启动开发模式Consul
- 确认Consul在端口8500运行
- 访问 http://localhost:8500 验证Consul UI

3. 设置环境变量
- 设置 `CONSUL_HOST` 为Consul主机地址（默认：localhost）
- 设置 `CONSUL_PORT` 为Consul端口（默认：8500）

4. 验证Smithery CLI工具
- 运行 `npx @smithery/cli --version` 确认工具可用
- Smithery CLI将自动下载

5. 使用Smithery安装Consul MCP服务
- 运行 `npx -y @smithery/cli install @kocierik/consul-mcp-server --client claude`
- Smithery将自动配置和安装服务

6. 验证服务运行
- 确认服务正常启动
- 通过MCP客户端测试Consul管理功能
