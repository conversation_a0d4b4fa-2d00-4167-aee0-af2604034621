{"branches": ["main"], "plugins": ["@semantic-release/commit-analyzer", "@semantic-release/release-notes-generator", "@semantic-release/changelog", ["@semantic-release/exec", {"prepareCmd": "node scripts/update-version.js ${nextRelease.version} && npm run build && chmod +x dist/index.js"}], ["@semantic-release/npm", {"npmPublish": true, "pkgRoot": "."}], ["@semantic-release/git", {"assets": ["package.json", "CHANGELOG.md", "src/index.ts", "src/cli/index.ts"], "message": "chore(release): ${nextRelease.version} [skip ci]\n\n${nextRelease.notes}"}], "@semantic-release/github"]}