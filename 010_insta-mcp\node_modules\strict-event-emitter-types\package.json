{"name": "strict-event-emitter-types", "version": "2.0.0", "description": "Strictly and safely type any EventEmitter-like interface on any object", "main": "dist/src/index.js", "scripts": {"build": "tsc", "prepublish": "npm run build"}, "types": "types/src", "repository": {"type": "git", "url": "git+https://github.com/bterlson/typed-event-emitter.git"}, "keywords": ["TypeScript", "TS", "EventEmitter", "events"], "files": ["types"], "author": "<PERSON> <<EMAIL>>", "license": "ISC", "bugs": {"url": "https://github.com/b<PERSON><PERSON>/typed-event-emitter/issues"}, "homepage": "https://github.com/b<PERSON><PERSON>/typed-event-emitter#readme", "dependencies": {}, "devDependencies": {"@types/node": "^9.4.6", "typescript": "next"}}