Render MCP - NPM 全局安装启动步骤

1. 安装Node.js和npm
- 访问 https://nodejs.org/ 下载并安装Node.js 18.0+
- npm通常随Node.js一起安装
- 验证安装：`node --version` 和 `npm --version`

2. 全局安装Render MCP包
- 运行 `npm install -g @niyogi/render-mcp`
- 等待安装完成
- 验证安装：`render-mcp --version`

3. 获取Render API密钥
- 访问 Render Dashboard (https://dashboard.render.com/account/api-keys)
- 创建新的API密钥
- 复制生成的API密钥

4. 设置环境变量
- 设置 `RENDER_API_KEY` 为获取的API密钥
- 确认环境变量设置正确

5. 启动Render MCP服务
- 运行 `render-mcp start`
- 服务将直接启动

6. 验证服务运行
- 确认服务正常启动
- 通过MCP客户端测试Render功能
