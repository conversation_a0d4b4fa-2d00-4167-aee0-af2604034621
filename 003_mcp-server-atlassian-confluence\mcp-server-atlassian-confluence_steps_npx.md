Atlassian Confluence MCP Server - NPX 启动步骤

1. 安装Node.js和npm
- 访问 https://nodejs.org/ 下载并安装Node.js 18.0+
- npm通常随Node.js一起安装
- 验证安装：`node --version` 和 `npm --version`

2. 获取Atlassian API凭据
- 访问 https://id.atlassian.com/manage-profile/security/api-tokens
- 点击 "Create API token"
- 输入描述性标签（如：mcp-confluence-access）
- 复制生成的API token

3. 准备Confluence信息
- 确定Confluence站点名称（如：mycompany.atlassian.net中的mycompany）
- 准备Atlassian账户邮箱地址
- 确认有Confluence访问权限

4. 设置环境变量
- 设置 `ATLASSIAN_SITE_NAME` 为站点名称
- 设置 `ATLASSIAN_USER_EMAIL` 为账户邮箱
- 设置 `ATLASSIAN_API_TOKEN` 为API token

5. 启动Confluence MCP服务
- 运行 `npx -y @aashari/mcp-server-atlassian-confluence`
- npx将自动下载并运行最新版本

6. 验证服务运行
- 确认服务正常启动
- 通过MCP客户端测试Confluence功能

