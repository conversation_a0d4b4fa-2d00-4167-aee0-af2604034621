## 1. 前置准备

### 先决条件
- Node.js 18.0 或更高版本
- npm 包管理器
- Git
- Chrome 浏览器

安装所需软件：
```bash
# 安装 Node.js
# 访问 https://nodejs.org 下载并安装最新版本

# 安装 Git
# 访问 https://git-scm.com 下载并安装

# 克隆项目并安装依赖
git clone https://github.com/mgsrevolver/consolespy.git
cd consolespy
npm install

# 运行设置脚本
./setup.sh
```

安装浏览器扩展：
1. 访问 Chrome Web Store 安装 ConsoleSpy 扩展
2. 或者在 Chrome 中加载 extension 文件夹作为开发者扩展

## 2. 配置环境变量

不需要额外配置

## 3. 启动服务

完成以上步骤后，点击下方的 **安装** 按钮连接并启动服务。

启动命令：
```bash
npx supergateway --port 8766 --stdio "node console-spy-mcp.js"
```
