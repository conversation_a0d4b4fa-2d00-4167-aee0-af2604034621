MCP Spotify - NPX 启动步骤

1. 安装Node.js和npm
- 访问 https://nodejs.org/ 下载并安装Node.js 18.0+
- npm通常随Node.js一起安装
- 验证安装：`node --version` 和 `npm --version`

2. 获取Spotify API凭据
- 访问 Spotify Developer Dashboard (https://developer.spotify.com/dashboard)
- 创建新应用
- 获取Client ID和Client Secret
- 配置重定向URI（如果需要）

3. 设置环境变量
- 设置 `SPOTIFY_CLIENT_ID` 为Client ID
- 设置 `SPOTIFY_CLIENT_SECRET` 为Client Secret
- 确认环境变量设置正确

4. 验证网络连接
- 确保能够访问Spotify API
- 测试基本的网络连通性
- 检查防火墙设置

5. 启动Spotify MCP服务
- 运行 `npx -y @thomaswawra/artistlens`
- npx将自动下载并运行最新版本

6. 验证服务运行
- 确认服务正常启动
- 通过MCP客户端测试Spotify功能

7. 测试Spotify功能
- 测试艺术家搜索
- 验证音乐数据获取

