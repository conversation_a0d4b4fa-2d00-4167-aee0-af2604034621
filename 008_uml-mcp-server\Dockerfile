# Generated by https://smithery.ai. See: https://smithery.ai/docs/config#dockerfile
FROM python:3.10-slim

# Set working directory
WORKDIR /app

# Copy requirements and source files
COPY requirements.txt ./
COPY . .

# Install dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Expose port if necessary (optional)
# EXPOSE 8000

# Command to run the MCP server
CMD ["python", "uml_mcp_server.py"]
