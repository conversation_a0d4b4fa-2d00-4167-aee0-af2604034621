## 1. 前置准备

### 先决条件
- Node.js 18.0 或更高版本
- npm 包管理器
- Consul 服务器

安装 Node.js：
```bash
# 访问 https://nodejs.org 下载并安装最新版本
# 或使用包管理器安装，例如：
# Windows: winget install OpenJS.NodeJS
# macOS: brew install node
# Ubuntu: sudo apt install nodejs npm
```

## 2. 配置环境变量

在 MCP NOW 客户端中设置以下环境变量：

**配置说明：**
- `CONSUL_HOST`: Consul 服务器主机地址（默认：localhost）
- `CONSUL_PORT`: Consul 服务器端口（默认：8500）

## 3. 启动服务

完成以上步骤后，点击下方的 **安装** 按钮连接并启动服务。

启动命令：
```bash
npx -y @smithery/cli install @kocierik/consul-mcp-server --client claude
```
