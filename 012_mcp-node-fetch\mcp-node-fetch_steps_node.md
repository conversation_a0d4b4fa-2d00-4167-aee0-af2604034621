MCP Node Fetch - Node 启动步骤

1. 安装Node.js和npm
- 访问 https://nodejs.org/ 下载并安装Node.js 18.0+
- npm通常随Node.js一起安装
- 验证安装：`node --version` 和 `npm --version`

2. 克隆源码并进入目录
- 运行 `git clone https://github.com/mcollina/mcp-node-fetch.git`
- 进入项目目录：`cd mcp-node-fetch`

3. 安装依赖并构建项目
- 运行 `npm install` 安装项目依赖
- 运行 `npm run build` 构建项目
- 确认dist目录生成成功

4. 验证构建结果
- 检查dist目录中的index.js文件
- 确认构建过程无错误

5. 启动MCP Node Fetch服务
- 运行 `node dist/index.js`
- 确保使用构建后文件的路径

6. 验证服务运行
- 确认服务正常启动
- 通过MCP客户端测试HTTP请求功能
