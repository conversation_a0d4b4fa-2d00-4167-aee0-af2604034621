## 1. Setup & Requirements

### Prerequisites
- Node.js 18.0 or higher
- npm package manager
- Git
- Tesla account with at least one vehicle
- Tesla API credentials (Client ID and Client Secret)
- <PERSON><PERSON> (for development and registration)

Install required software:
```bash
# Install Node.js
# Visit https://nodejs.org to download and install the latest version

# Install Git
# Visit https://git-scm.com to download and install

# Install Ngrok
# Visit https://ngrok.com/download to download and install

# Clone project and install dependencies
git clone https://github.com/scald/tesla-mcp.git
cd tesla-mcp
npm install
npm run build
```

Get Tesla API credentials:
1. Visit Tesla Developer Portal (https://developer.tesla.com/)
2. Create an app and get Client ID and Client Secret
3. Run `npm run get-token` to get refresh token
4. Run `npm run register` to register the app

## 2. Configure Environment Variables

Configure the following environment variables in MCP NOW client:

**Configuration Details:**
- `TESLA_CLIENT_ID`: Tesla API Client ID
- `TESLA_CLIENT_SECRET`: Tesla API Client Secret
- `TESLA_REFRESH_TOKEN`: Tesla API Refresh Token

## 3. Launch the Server

After completing the above steps, click the **Install** button below to connect and start the service.

Launch command:
```bash
node build/index.js
```
