## 1. Setup & Requirements

### Prerequisites
- Node.js 18.0 or higher
- npm package manager
- Consul server

Install Node.js:
```bash
# Visit https://nodejs.org to download and install the latest version
# Or use package managers, for example:
# Windows: winget install OpenJS.NodeJS
# macOS: brew install node
# Ubuntu: sudo apt install nodejs npm
```

## 2. Configure Environment Variables

Configure the following environment variables in MCP NOW client:

**Configuration Details:**
- `CONSUL_HOST`: Consul server host (default: localhost)
- `CONSUL_PORT`: Consul server port (default: 8500)

## 3. Launch the Server

After completing the above steps, click the **Install** button below to connect and start the service.

Launch command:
```bash
npx -y @smithery/cli install @kocierik/consul-mcp-server --client claude
```
