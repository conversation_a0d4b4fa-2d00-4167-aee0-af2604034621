## 1. 前置准备

### 先决条件
- Node.js 18.0 或更高版本
- npm 包管理器
- Git
- Microsoft Azure 应用注册

安装所需软件：
```bash
# 安装 Node.js
# 访问 https://nodejs.org 下载并安装最新版本

# 安装 Git
# 访问 https://git-scm.com 下载并安装

# 克隆项目并安装依赖
git clone https://github.com/anoopt/outlook-meetings-scheduler-mcp-server.git
cd outlook-meetings-scheduler-mcp-server
npm install
npm run build
```

设置 Microsoft Azure 应用：
1. 在 Azure Portal 中注册新应用
2. 创建客户端密钥
3. 授予必要权限（Microsoft Graph API > Application permissions > Calendars.ReadWrite, People.Read.All, User.ReadBasic.All）
4. 记录 Client ID、Client Secret 和 Tenant ID

## 2. 配置环境变量

在 MCP NOW 客户端中设置以下环境变量：

**配置说明：**
- `CLIENT_ID`: Azure 应用的 Client ID
- `CLIENT_SECRET`: Azure 应用的 Client Secret
- `TENANT_ID`: Azure 租户 ID
- `USER_EMAIL`: 用户邮箱地址

## 3. 启动服务

完成以上步骤后，点击下方的 **安装** 按钮连接并启动服务。

启动命令：
```bash
node build/index.js
```
