# Dependency directories
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log
.pnpm-debug.log

# Build output
build/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE and editor files
.idea/
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
.DS_Store

# TypeScript cache
*.tsbuildinfo

# Logs
logs
*.log

# Coverage directory used by tools like istanbul
coverage/

# Misc
.cache/
.vercel
.netlify
dist/
