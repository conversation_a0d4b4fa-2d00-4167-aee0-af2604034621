{"name": "fastmcp", "version": "1.20.5", "main": "dist/FastMCP.js", "scripts": {"build": "tsup", "test": "vitest run && tsc && jsr publish --dry-run", "format": "prettier --write . && eslint --fix ."}, "bin": {"fastmcp": "dist/bin/fastmcp.js"}, "keywords": ["MCP", "SSE"], "type": "module", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "description": "A TypeScript framework for building MCP servers.", "module": "dist/FastMCP.js", "types": "dist/FastMCP.d.ts", "dependencies": {"@modelcontextprotocol/sdk": "^1.6.0", "execa": "^9.5.2", "file-type": "^20.3.0", "fuse.js": "^7.1.0", "mcp-proxy": "^2.10.4", "strict-event-emitter-types": "^2.0.0", "undici": "^7.4.0", "uri-templates": "^0.2.0", "yargs": "^17.7.2", "zod": "^3.24.2", "zod-to-json-schema": "^3.24.3"}, "repository": {"url": "https://github.com/punkpeye/fastmcp"}, "homepage": "https://glama.ai/mcp", "release": {"branches": ["main"], "plugins": ["@semantic-release/commit-analyzer", "@semantic-release/release-notes-generator", "@semantic-release/npm", "@semantic-release/github", "@sebbo2002/semantic-release-jsr"]}, "devDependencies": {"@sebbo2002/semantic-release-jsr": "^2.0.4", "@tsconfig/node22": "^22.0.0", "@types/node": "^22.13.5", "@types/uri-templates": "^0.1.34", "@types/yargs": "^17.0.33", "eslint": "^9.21.0", "eslint-plugin-perfectionist": "^4.9.0", "eventsource-client": "^1.1.3", "get-port-please": "^3.1.2", "jsr": "^0.13.3", "prettier": "^3.5.2", "semantic-release": "^24.2.3", "tsup": "^8.4.0", "typescript": "^5.7.3", "vitest": "^3.0.7"}, "tsup": {"entry": ["src/FastMCP.ts", "src/bin/fastmcp.ts"], "format": ["esm"], "dts": true, "splitting": true, "sourcemap": true, "clean": true}}