name: CI - Dependabot Auto-merge

on:
    pull_request:
        branches: [main]

permissions:
    contents: write
    pull-requests: write
    checks: read

jobs:
    auto-merge-dependabot:
        runs-on: ubuntu-latest
        if: github.actor == 'dependabot[bot]'
        steps:
            - name: Checkout code
              uses: actions/checkout@v4

            - name: Setup Node.js
              uses: actions/setup-node@v4
              with:
                  node-version: '22'
                  cache: 'npm'

            - name: Install dependencies
              run: npm ci

            - name: Run tests
              run: npm test

            - name: Run linting
              run: npm run lint

            - name: Auto-approve PR
              uses: hmarr/auto-approve-action@v4
              with:
                  github-token: ${{ secrets.GITHUB_TOKEN }}

            - name: Enable auto-merge
              if: success()
              run: gh pr merge --auto --merge "$PR_URL"
              env:
                  PR_URL: ${{ github.event.pull_request.html_url }}
                  GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
