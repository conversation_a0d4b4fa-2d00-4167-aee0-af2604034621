Maven MCP Server - SSE 启动步骤

1. 安装Node.js和npm
- 访问 https://nodejs.org/ 下载并安装Node.js 18.0+
- npm通常随Node.js一起安装
- 验证安装：`node --version` 和 `npm --version`

2. 验证网络连接
- 确保能够访问npm registry
- 测试基本的网络连通性
- 检查防火墙设置

3. 选择启动模式
- 本地访问：使用默认端口3000
- 远程访问：绑定到0.0.0.0地址

4. 启动SSE服务（本地访问）
- 运行 `npx mcp-maven-deps --port=3000`
- 服务将在本地端口3000启动

5. 启动SSE服务（远程访问）
- 运行 `npx mcp-maven-deps --host=0.0.0.0 --port=3000`
- 服务将绑定到所有网络接口

6. 验证服务运行
- 访问 http://localhost:3000 确认服务启动
- 通过MCP客户端测试SSE连接

7. 测试功能
- 测试Maven项目分析
- 验证依赖解析功能

