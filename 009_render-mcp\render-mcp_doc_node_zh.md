## 1. 前置准备

### 先决条件
- Node.js 18.0 或更高版本
- npm 包管理器
- Git
- Render.com API 密钥

安装所需软件：
```bash
# 安装 Node.js
# 访问 https://nodejs.org 下载并安装最新版本

# 安装 Git
# 访问 https://git-scm.com 下载并安装

# 克隆项目并安装依赖
git clone https://github.com/niyogi/render-mcp.git
cd render-mcp
npm install
```

获取 Render API 密钥：
1. 访问 Render Dashboard (https://dashboard.render.com/account/api-keys)
2. 创建新的 API 密钥
3. 复制生成的 API 密钥

## 2. 配置环境变量

在 MCP NOW 客户端中设置以下环境变量：

**配置说明：**
- `RENDER_API_KEY`: Render.com API 密钥

## 3. 启动服务

完成以上步骤后，点击下方的 **安装** 按钮连接并启动服务。

启动命令：
```bash
node bin/render-mcp.js start
```
