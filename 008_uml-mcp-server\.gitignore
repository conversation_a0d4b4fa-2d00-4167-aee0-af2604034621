# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
ENV/
env/
.env

# IDE
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# Database
*.db
*.sqlite3

# Logs
*.log
logs/

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# Flask
instance/
.webassets-cache

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# mypy
.mypy_cache/

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Local development settings
local_settings.py 

logs/

.cursor

# uml-output
uml-output/
uml-mcp-venv/
