Brave MCP Search - NPX 启动步骤

1. 安装Node.js和npm
- 访问 https://nodejs.org/ 下载并安装Node.js 18.0+
- npm通常随Node.js一起安装
- 验证安装：`node --version` 和 `npm --version`

2. 获取Brave Search API密钥
- 访问 Brave Search API 官网注册账户
- 创建新的API密钥
- 复制生成的API密钥
- 确认API配额和使用限制

3. 设置环境变量
- 设置 `BRAVE_API_KEY` 为获取的API密钥
- 确认环境变量设置正确

4. 验证网络连接
- 确保能够访问Brave Search API
- 测试基本的网络连通性
- 检查防火墙设置

5. 启动Brave Search MCP服务
- 运行 `npx -y @smithery/cli install @arben-adm/brave-mcp-search --client claude`
- Smithery将自动配置和安装服务

6. 验证服务运行
- 确认服务正常启动
- 通过MCP客户端测试搜索功能

7. 测试搜索功能
- 执行基本搜索查询
- 验证搜索结果返回

