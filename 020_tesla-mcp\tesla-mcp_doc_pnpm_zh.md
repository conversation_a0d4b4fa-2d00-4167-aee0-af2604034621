## 1. 前置准备

### 先决条件
- Node.js 18.0 或更高版本
- npm 包管理器
- Git
- Tesla 账户和至少一辆车
- Tesla API 凭据（Client ID 和 Client Secret）
- Ngrok（用于开发和注册）

安装所需软件：
```bash
# 安装 Node.js
# 访问 https://nodejs.org 下载并安装最新版本

# 安装 Git
# 访问 https://git-scm.com 下载并安装

# 安装 Ngrok
# 访问 https://ngrok.com/download 下载并安装

# 克隆项目并安装依赖
git clone https://github.com/scald/tesla-mcp.git
cd tesla-mcp
npm install
npm run build
```

获取 Tesla API 凭据：
1. 访问 Tesla Developer Portal (https://developer.tesla.com/)
2. 创建应用并获取 Client ID 和 Client Secret
3. 运行 `npm run get-token` 获取 refresh token
4. 运行 `npm run register` 注册应用

## 2. 配置环境变量

在 MCP NOW 客户端中设置以下环境变量：

**配置说明：**
- `TESLA_CLIENT_ID`: Tesla API Client ID
- `TESLA_CLIENT_SECRET`: Tesla API Client Secret
- `TESLA_REFRESH_TOKEN`: Tesla API Refresh Token

## 3. 启动服务

完成以上步骤后，点击下方的 **安装** 按钮连接并启动服务。

启动命令：
```bash
node build/index.js
```
