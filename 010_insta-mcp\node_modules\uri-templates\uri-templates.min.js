!function(a,b){"function"==typeof define&&define.amd?define("uri-templates",[],b):"undefined"!=typeof module&&module.exports?module.exports=b():a.UriTemplate=b()}(this,function(){function a(a){return encodeURI(a).replace(/%25[0-9][0-9]/g,function(a){return"%"+a.substring(3)})}function b(a){return a=a.replace(/%../g,""),encodeURIComponent(a)===a}function c(c){var d="";e[c.charAt(0)]&&(d=c.charAt(0),c=c.substring(1));var g="",h="",i=!0,j=!1,k=!1;"+"==d?i=!1:"."==d?(h=".",g="."):"/"==d?(h="/",g="/"):"#"==d?(h="#",i=!1):";"==d?(h=";",g=";",j=!0,k=!0):"?"==d?(h="?",g="&",j=!0):"&"==d&&(h="&",g="&",j=!0);for(var l=[],m=c.split(","),n=[],o={},p=0;p<m.length;p++){var q=m[p],r=null;if(-1!=q.indexOf(":")){var s=q.split(":");q=s[0],r=parseInt(s[1])}for(var t={};f[q.charAt(q.length-1)];)t[q.charAt(q.length-1)]=!0,q=q.substring(0,q.length-1);var u={truncate:r,name:q,suffices:t};n.push(u),o[q]=u,l.push(q)}var v=function(b){for(var c="",d=0,e=0;e<n.length;e++){var f=n[e],l=b(f.name);if(null==l||Array.isArray(l)&&0==l.length||"object"==typeof l&&0==Object.keys(l).length)d++;else if(c+=e==d?h:g||",",Array.isArray(l)){j&&(c+=f.name+"=");for(var m=0;m<l.length;m++)m>0&&(c+=f.suffices["*"]?g||",":",",f.suffices["*"]&&j&&(c+=f.name+"=")),c+=i?encodeURIComponent(l[m]).replace(/!/g,"%21"):a(l[m])}else if("object"==typeof l){j&&!f.suffices["*"]&&(c+=f.name+"=");var o=!0;for(var p in l)o||(c+=f.suffices["*"]?g||",":","),o=!1,c+=i?encodeURIComponent(p).replace(/!/g,"%21"):a(p),c+=f.suffices["*"]?"=":",",c+=i?encodeURIComponent(l[p]).replace(/!/g,"%21"):a(l[p])}else j&&(c+=f.name,k&&""==l||(c+="=")),null!=f.truncate&&(l=l.substring(0,f.truncate)),c+=i?encodeURIComponent(l).replace(/!/g,"%21"):a(l)}return c},w=function(a,c,d){if(h&&(a=a.substring(h.length)),1==n.length&&n[0].suffices["*"]){for(var e=n[0],f=e.name,k=e.suffices["*"]?a.split(g||","):[a],l=i&&-1!=a.indexOf("="),m=1;m<k.length;m++){var a=k[m];l&&-1==a.indexOf("=")&&(k[m-1]+=(g||",")+a,k.splice(m,1),m--)}for(var m=0;m<k.length;m++){var a=k[m];i&&-1!=a.indexOf("=")&&(l=!0);var p=a.split(",");1==p.length?k[m]=p[0]:k[m]=p}if(j||l){for(var q=c[f]||{},r=0;r<k.length;r++){var s=a;if(!j||s){if("string"==typeof k[r]){var a=k[r],t=a.split("=",1)[0],a=a.substring(t.length+1);if(i){if(d&&!b(a))return;a=decodeURIComponent(a)}s=a}else{var a=k[r][0],t=a.split("=",1)[0],a=a.substring(t.length+1);if(i){if(d&&!b(a))return;a=decodeURIComponent(a)}k[r][0]=a,s=k[r]}if(i){if(d&&!b(t))return;t=decodeURIComponent(t)}void 0!==q[t]?Array.isArray(q[t])?q[t].push(s):q[t]=[q[t],s]:q[t]=s}}1==Object.keys(q).length&&void 0!==q[f]?c[f]=q[f]:c[f]=q}else{if(i)for(var r=0;r<k.length;r++){var p=k[r];if(Array.isArray(p))for(var u=0;u<p.length;u++){if(d&&!b(p[u]))return;p[u]=decodeURIComponent(p[u])}else{if(d&&!b(p))return;k[r]=decodeURIComponent(p)}}void 0!==c[f]?Array.isArray(c[f])?c[f]=c[f].concat(k):c[f]=[c[f]].concat(k):1!=k.length||e.suffices["*"]?c[f]=k:c[f]=k[0]}}else{for(var k=1==n.length?[a]:a.split(g||","),v={},m=0;m<k.length;m++){for(var w=0;w<n.length-1&&m>w&&!n[w].suffices["*"];w++);if(w!=m){for(var x=n.length-1;x>0&&n.length-x<k.length-m&&!n[x].suffices["*"];x--);n.length-x!=k.length-m?v[m]=w:v[m]=x}else v[m]=m}for(var m=0;m<k.length;m++){var a=k[m];if(a||!j){var p=a.split(","),l=!1;if(j){var a=p[0],f=a.split("=",1)[0],a=a.substring(f.length+1);p[0]=a;var e=o[f]||n[0]}else var e=n[v[m]],f=e.name;for(var r=0;r<p.length;r++)if(i){if(d&&!b(p[r]))return;p[r]=decodeURIComponent(p[r])}(j||e.suffices["*"])&&void 0!==c[f]?Array.isArray(c[f])?c[f]=c[f].concat(p):c[f]=[c[f]].concat(p):1!=p.length||e.suffices["*"]?c[f]=p:c[f]=p[0]}}}return 1};return{varNames:l,prefix:h,substitution:v,unSubstitution:w}}function d(a){if(!(this instanceof d))return new d(a);for(var b=a.split("{"),e=[b.shift()],f=[],g=[],h=[],i=[];b.length>0;){var j=b.shift(),k=j.split("}")[0],l=j.substring(k.length+1),m=c(k);g.push(m.substitution),h.push(m.unSubstitution),f.push(m.prefix),e.push(l),i=i.concat(m.varNames)}this.fill=function(a){if(a&&"function"!=typeof a){var b=a;a=function(a){return b[a]}}for(var c=e[0],d=0;d<g.length;d++){var f=g[d];c+=f(a),c+=e[d+1]}return c},this.fromUri=function(a,b){b=b||{};for(var c={},d=0;d<e.length;d++){var g=e[d];if(a.substring(0,g.length)!==g)return;if(a=a.substring(g.length),d>=e.length-1){if(""==a)break;return}var i=f[d];if(!i||a.substring(0,i.length)===i){for(var j=e[d+1],k=d;;){if(k==e.length-2){var l=a.substring(a.length-j.length);if(l!==j)return;var m=a.substring(0,a.length-j.length);a=l}else if(j){var n=a.indexOf(j),m=a.substring(0,n);a=a.substring(n)}else if(f[k+1]){var n=a.indexOf(f[k+1]);-1===n&&(n=a.length);var m=a.substring(0,n);a=a.substring(n)}else{if(e.length>k+2){k++,j=e[k+1];continue}var m=a;a=""}break}if(!h[d](m,c,b.strict))return}}return c},this.varNames=i,this.template=a}var e={"+":!0,"#":!0,".":!0,"/":!0,";":!0,"?":!0,"&":!0},f={"*":!0};return d.prototype={toString:function(){return this.template},fillFromObject:function(a){return this.fill(a)},test:function(a,b){return!!this.fromUri(a,b)}},d});