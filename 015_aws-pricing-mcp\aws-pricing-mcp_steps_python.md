AWS Pricing MCP - Python 启动步骤

1. 安装Python和Git
- 访问 https://python.org/ 下载并安装Python 3.8+
- 访问 https://git-scm.com/ 下载并安装Git
- pip通常随Python一起安装
- 验证安装：`python --version` 和 `pip --version`

2. 克隆源码并进入目录
- 运行 `git clone https://github.com/trilogy-group/aws-pricing-mcp.git`
- 进入项目目录：`cd aws-pricing-mcp`

3. 安装项目依赖
- 运行 `pip install -r requirements.txt`
- 等待安装完成
- 验证依赖安装成功

4. 验证项目结构
- 检查 `src/server.py` 文件是否存在
- 确认项目配置正确

5. 启动AWS Pricing MCP服务
- 运行 `python src/server.py`
- 服务将直接启动

6. 验证服务运行
- 确认服务正常启动
- 通过MCP客户端测试AWS定价查询功能
