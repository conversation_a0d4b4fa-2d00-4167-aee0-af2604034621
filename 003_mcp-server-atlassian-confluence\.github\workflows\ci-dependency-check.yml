name: CI - Dependency Check

on:
    schedule:
        - cron: '0 5 * * 1' # Run at 5 AM UTC every Monday
    workflow_dispatch: # Allow manual triggering

jobs:
    validate:
        runs-on: ubuntu-latest
        steps:
            - name: Checkout code
              uses: actions/checkout@v4

            - name: Setup Node.js
              uses: actions/setup-node@v4
              with:
                  node-version: '22'
                  cache: 'npm'

            - name: Install dependencies
              run: npm ci

            - name: Run npm audit
              run: npm audit

            - name: Check for outdated dependencies
              run: npm outdated || true

            - name: Run tests
              run: npm test

            - name: Run linting
              run: npm run lint

            - name: Build project
              run: npm run build
