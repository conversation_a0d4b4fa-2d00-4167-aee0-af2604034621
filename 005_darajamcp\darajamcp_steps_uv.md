Daraja MCP - UV 启动步骤

1. 安装Python和uv
- 访问 https://python.org/ 下载并安装Python 3.12
- 安装uv包管理器：
  - Mac/Linux: `curl -LsSf https://astral.sh/uv/install.sh | sh`
  - Windows: `powershell -ExecutionPolicy ByPass -c "irm https://astral.sh/uv/install.ps1 | iex"`
- 验证安装：`python --version` 和 `uv --version`

2. 克隆源码并设置环境
- 运行 `git clone https://github.com/jameskanyiri/DarajaMCP.git`
- 进入项目目录：`cd DarajaMCP`
- 创建虚拟环境：`uv venv`

3. 激活环境并安装依赖
- 激活虚拟环境：`.venv\Scripts\activate` (Windows) 或 `source .venv/bin/activate` (Linux/Mac)
- 同步依赖：`uv sync`

4. 获取Safaricom Daraja API凭据
- 访问 Safaricom Daraja API 开发者门户
- 注册账户并创建应用
- 获取Consumer Key和Consumer Secret
- 选择环境（sandbox或production）

5. 设置环境变量
- 设置 `DARAJA_CONSUMER_KEY` 为Consumer Key
- 设置 `DARAJA_CONSUMER_SECRET` 为Consumer Secret
- 设置 `DARAJA_ENVIRONMENT` 为环境设置

6. 启动Daraja MCP服务
- 运行 `uv --directory /absolute/path/to/DarajaMCP run main.py`
- 确保使用项目的绝对路径

7. 验证服务运行
- 确认服务正常启动
- 通过MCP客户端测试Daraja API功能

