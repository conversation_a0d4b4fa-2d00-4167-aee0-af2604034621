## 1. 前置准备

### 先决条件
- Node.js 18.0 或更高版本
- Atlassian 账户，可访问 Confluence Cloud

安装 Node.js 和全局包：
```bash
# 安装 Node.js
# 访问 https://nodejs.org 下载并安装最新版本

# 全局安装包
npm install -g @aashari/mcp-server-atlassian-confluence
```

获取 Atlassian API Token：
1. 访问 https://id.atlassian.com/manage-profile/security/api-tokens
2. 点击 **Create API token**
3. 输入描述性标签（如：mcp-confluence-access）
4. 点击 **Create**
5. 复制生成的 API token（只显示一次）

## 2. 配置环境变量

在 MCP NOW 客户端中设置以下环境变量：

**配置说明：**
- `ATLASSIAN_SITE_NAME`: Confluence 站点名称（如：mycompany，对应 mycompany.atlassian.net）
- `ATLASSIAN_USER_EMAIL`: Atlassian 账户邮箱
- `ATLASSIAN_API_TOKEN`: 从步骤1获取的 API token

## 3. 启动服务

完成以上步骤后，点击下方的 **安装** 按钮连接并启动服务。

启动命令：
```bash
mcp-atlassian-confluence
```
