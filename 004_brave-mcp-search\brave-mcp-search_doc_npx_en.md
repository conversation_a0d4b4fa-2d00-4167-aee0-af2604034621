## 1. Setup & Requirements

### Prerequisites
- Node.js 18.0 or higher
- npm package manager
- Brave Search API key

Install Node.js:
```bash
# Visit https://nodejs.org to download and install the latest version
# Or use package managers, for example:
# Windows: winget install OpenJS.NodeJS
# macOS: brew install node
# Ubuntu: sudo apt install nodejs npm
```

Get Brave Search API key:
1. Visit Brave Search API website to register an account
2. Create a new API key
3. Copy the generated API key

## 2. Configure Environment Variables

Configure the following environment variables in MCP NOW client:

**Configuration Details:**
- `BRAVE_API_KEY`: Brave Search API key

## 3. Launch the Server

After completing the above steps, click the **Install** button below to connect and start the service.

Launch command:
```bash
npx -y @smithery/cli install @arben-adm/brave-mcp-search --client claude
```
