#!/usr/bin/env node

import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';

async function testMCPRepl() {
    console.log('🧪 测试MCP REPL服务器...');
    
    try {
        // 创建客户端传输
        const transport = new StdioClientTransport({
            command: 'node',
            args: ['src/direct-executor-server.js']
        });

        // 创建客户端
        const client = new Client({
            name: "test-repl-client",
            version: "1.0.0"
        }, {
            capabilities: {}
        });

        // 连接到服务器
        console.log('🔗 正在连接到MCP REPL服务器...');
        await client.connect(transport);
        console.log('✅ 连接成功！');

        // 列出可用工具
        console.log('🛠️  正在获取可用工具...');
        const tools = await client.listTools();
        console.log(`✅ 发现 ${tools.tools.length} 个工具:`);
        
        tools.tools.forEach((tool, index) => {
            console.log(`  ${index + 1}. ${tool.name}`);
            console.log(`     描述: ${tool.description}`);
        });

        // 测试一个简单的代码执行（如果有相关工具）
        const executeTools = tools.tools.filter(tool => 
            tool.name.includes('execute') || 
            tool.name.includes('run') || 
            tool.name.includes('eval')
        );

        if (executeTools.length > 0) {
            console.log('\n🚀 测试代码执行功能...');
            try {
                const result = await client.callTool({
                    name: executeTools[0].name,
                    arguments: { 
                        code: 'console.log("Hello from MCP REPL!");',
                        language: 'javascript'
                    }
                });
                console.log('✅ 代码执行成功！');
                console.log('📄 执行结果:', result.content);
            } catch (error) {
                console.log('⚠️  代码执行测试失败:', error.message);
            }
        }

        // 关闭连接
        await client.close();
        console.log('✅ 测试完成，连接已关闭');

        return true;

    } catch (error) {
        console.error('❌ MCP REPL测试失败:', error.message);
        return false;
    }
}

// 运行测试
testMCPRepl().then(success => {
    if (success) {
        console.log('\n🎉 MCP REPL测试成功！');
        process.exit(0);
    } else {
        console.log('\n💥 MCP REPL测试失败');
        process.exit(1);
    }
}).catch(error => {
    console.error('💥 测试过程中发生未处理的错误:', error);
    process.exit(1);
});
