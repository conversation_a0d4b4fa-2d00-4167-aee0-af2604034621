## 1. Setup & Requirements

### Prerequisites
- AWS CLI
- SAM CLI
- Python 3.8 or higher
- Git
- AWS account and credentials

Install required software:
```bash
# Install AWS CLI
# Visit https://aws.amazon.com/cli/ to download and install

# Install SAM CLI
# Visit https://docs.aws.amazon.com/serverless-application-model/latest/developerguide/install-sam-cli.html

# Install Python 3.8+
# Visit https://python.org to download and install

# Install Git
# Visit https://git-scm.com to download and install

# Clone project
git clone https://github.com/trilogy-group/aws-pricing-mcp.git
cd aws-pricing-mcp
```

## 2. Configure Environment Variables

Configure the following environment variables in MCP NOW client:

**Configuration Details:**
- `AWS_ACCESS_KEY_ID`: AWS access key ID
- `AWS_SECRET_ACCESS_KEY`: AWS secret access key
- `AWS_DEFAULT_REGION`: AWS default region

## 3. Launch the Server

After completing the above steps, click the **Install** button below to connect and start the service.

Deploy command:
```bash
sam build
sam deploy --guided
```
