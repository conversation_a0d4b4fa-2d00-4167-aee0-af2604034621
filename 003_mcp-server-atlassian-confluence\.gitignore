# Dependency directories
node_modules/
.npm

# TypeScript output
dist/
build/
*.tsbuildinfo

# Coverage directories
coverage/
.nyc_output/

# Environment variables
.env
.env.local
.env.*.local

# Log files
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# IDE files
.idea/
.vscode/
*.sublime-project
*.sublime-workspace
.project
.classpath
.settings/
.DS_Store

# Temp directories
.tmp/
temp/

# Backup files
*.bak

# Editor directories and files
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# macOS
.DS_Store

# Misc
.yarn-integrity
repomix-output.txt
