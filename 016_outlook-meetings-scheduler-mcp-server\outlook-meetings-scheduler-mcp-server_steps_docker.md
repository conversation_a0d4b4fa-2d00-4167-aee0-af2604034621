Outlook Meetings Scheduler MCP Server - Docker 启动步骤

1. 安装Docker和Git
- 访问 https://docker.com/ 下载并安装Docker
- 访问 https://git-scm.com/ 下载并安装Git
- 验证安装：`docker --version` 和 `git --version`

2. 克隆源码并进入目录
- 运行 `git clone https://github.com/anoopt/outlook-meetings-scheduler-mcp-server.git`
- 进入项目目录：`cd outlook-meetings-scheduler-mcp-server`

3. 构建Docker镜像
- 运行 `docker build -t mcp/outlook-meetings-scheduler .`
- 等待镜像构建完成
- 验证镜像构建成功：`docker images`

4. 设置Microsoft Azure应用
- 在Azure Portal中注册新应用
- 创建客户端密钥
- 授予必要权限（Microsoft Graph API > Application permissions > Calendars.ReadWrite, People.Read.All, User.ReadBasic.All）
- 记录Client ID、Client Secret和Tenant ID

5. 准备环境变量
- 准备 `CLIENT_ID` 为Azure应用的Client ID
- 准备 `CLIENT_SECRET` 为Azure应用的Client Secret
- 准备 `TENANT_ID` 为Azure租户ID
- 准备 `USER_EMAIL` 为用户邮箱地址

6. 启动Docker容器
- 运行 `docker run -i --rm -e CLIENT_ID -e CLIENT_SECRET -e TENANT_ID -e USER_EMAIL mcp/outlook-meetings-scheduler`
- 容器将自动启动服务

7. 验证服务运行
- 确认容器正常运行
- 通过MCP客户端测试Outlook会议调度功能
