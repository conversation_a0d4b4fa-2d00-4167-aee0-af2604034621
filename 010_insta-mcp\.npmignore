# Git and GitHub related files
.git/
.github/
.gitignore

# Development and test files
__pycache__/
*.pyc
*.pyo
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg
.env
.venv
venv/
ENV/

# Sensitive data (but keep the example file)
instagram_cookies.json
instagram_cookies-example.json

# IDE specific files
.idea/
.vscode/
*.swp
*.swo

# Logs
logs/
*.log

# Don't include npm debugging logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
