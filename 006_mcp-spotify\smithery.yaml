# Smithery configuration file: https://smithery.ai/docs/config#smitheryyaml

startCommand:
  type: stdio
  configSchema:
    # JSON Schema defining the configuration options for the MCP.
    type: object
    required:
      - spotifyClientId
      - spotifyClientSecret
    properties:
      spotifyClientId:
        type: string
        description: Spotify API Client ID
      spotifyClientSecret:
        type: string
        description: Spotify API Client Secret
  commandFunction:
    # A function that produces the CLI command to start the MCP on stdio.
    |-
    (config) => ({ command: 'node', args: ['build/index.js'], env: { SPOTIFY_CLIENT_ID: config.spotifyClientId, SPOTIFY_CLIENT_SECRET: config.spotifyClientSecret } })