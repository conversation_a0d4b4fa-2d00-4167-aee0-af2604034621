## 1. Setup & Requirements

### Prerequisites
- Docker
- Git
- Microsoft Azure app registration

Install required software:
```bash
# Install Docker
# Visit https://docker.com to download and install

# Install Git
# Visit https://git-scm.com to download and install

# Clone project and build Docker image
git clone https://github.com/anoopt/outlook-meetings-scheduler-mcp-server.git
cd outlook-meetings-scheduler-mcp-server
docker build -t mcp/outlook-meetings-scheduler .
```

Setup Microsoft Azure app:
1. Register a new app in Azure Portal
2. Create a client secret
3. Grant necessary permissions (Microsoft Graph API > Application permissions > Calendars.ReadWrite, People.Read.All, User.ReadBasic.All)
4. Note your Client ID, Client Secret, and Tenant ID

## 2. Configure Environment Variables

Configure the following environment variables in MCP NOW client:

**Configuration Details:**
- `CLIENT_ID`: Azure app Client ID
- `CLIENT_SECRET`: Azure app Client Secret
- `TENANT_ID`: Azure Tenant ID
- `USER_EMAIL`: User email address

## 3. Launch the Server

After completing the above steps, click the **Install** button below to connect and start the service.

Launch command:
```bash
docker run -i --rm -e CLIENT_ID -e CLIENT_SECRET -e TENANT_ID -e USER_EMAIL mcp/outlook-meetings-scheduler
```
