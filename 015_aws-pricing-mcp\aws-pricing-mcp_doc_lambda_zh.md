## 1. 前置准备

### 先决条件
- AWS CLI
- SAM CLI
- Python 3.8 或更高版本
- Git
- AWS 账户和凭据

安装所需软件：
```bash
# 安装 AWS CLI
# 访问 https://aws.amazon.com/cli/ 下载并安装

# 安装 SAM CLI
# 访问 https://docs.aws.amazon.com/serverless-application-model/latest/developerguide/install-sam-cli.html

# 安装 Python 3.8+
# 访问 https://python.org 下载并安装

# 安装 Git
# 访问 https://git-scm.com 下载并安装

# 克隆项目
git clone https://github.com/trilogy-group/aws-pricing-mcp.git
cd aws-pricing-mcp
```

## 2. 配置环境变量

在 MCP NOW 客户端中设置以下环境变量：

**配置说明：**
- `AWS_ACCESS_KEY_ID`: AWS 访问密钥 ID
- `AWS_SECRET_ACCESS_KEY`: AWS 秘密访问密钥
- `AWS_DEFAULT_REGION`: AWS 默认区域

## 3. 启动服务

完成以上步骤后，点击下方的 **安装** 按钮连接并启动服务。

部署命令：
```bash
sam build
sam deploy --guided
```
