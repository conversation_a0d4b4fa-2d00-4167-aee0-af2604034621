name: CI - Semantic Release

# This workflow is triggered on every push to the main branch
# It analyzes commits and automatically releases a new version when needed
on:
    push:
        branches: [main]

jobs:
    release:
        name: Semantic Release
        runs-on: ubuntu-latest
        # Permissions needed for creating releases, updating issues, and publishing packages
        permissions:
            contents: write # Needed to create releases and tags
            issues: write # Needed to comment on issues
            pull-requests: write # Needed to comment on pull requests
            # packages permission removed as we're not using GitHub Packages
        steps:
            # Step 1: Check out the full Git history for proper versioning
            - name: Checkout
              uses: actions/checkout@v4
              with:
                  fetch-depth: 0 # Fetches all history for all branches and tags

            # Step 2: Setup Node.js environment
            - name: Setup Node.js
              uses: actions/setup-node@v4
              with:
                  node-version: 22 # Using Node.js 22
                  cache: 'npm' # Enable npm caching

            # Step 3: Install dependencies with clean install
            - name: Install dependencies
              run: npm ci # Clean install preserving package-lock.json

            # Step 4: Build the package
            - name: Build
              run: npm run build # Compiles TypeScript to JavaScript

            # Step 5: Ensure executable permissions
            - name: Set executable permissions
              run: chmod +x dist/index.js

            # Step 6: Run tests to ensure quality
            - name: Verify tests
              run: npm test # Runs Jest tests

            # Step 7: Configure Git identity for releases
            - name: Configure Git User
              run: |
                  git config --global user.email "github-actions[bot]@users.noreply.github.com"
                  git config --global user.name "github-actions[bot]"

            # Step 8: Run semantic-release to analyze commits and publish to npm
            - name: Semantic Release
              id: semantic
              env:
                  # Tokens needed for GitHub and npm authentication
                  GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }} # For creating releases and commenting
                  NPM_TOKEN: ${{ secrets.NPM_TOKEN }} # For publishing to npm
              run: |
                  echo "Running semantic-release for version bump and npm publishing"
                  npx semantic-release

                  # Note: GitHub Packages publishing has been removed
