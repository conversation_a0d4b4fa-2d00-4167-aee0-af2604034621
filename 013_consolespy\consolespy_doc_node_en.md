## 1. Setup & Requirements

### Prerequisites
- Node.js 18.0 or higher
- npm package manager
- Git
- Chrome browser

Install required software:
```bash
# Install Node.js
# Visit https://nodejs.org to download and install the latest version

# Install Git
# Visit https://git-scm.com to download and install

# Clone project and install dependencies
git clone https://github.com/mgsrevolver/consolespy.git
cd consolespy
npm install

# Run setup script
./setup.sh
```

Install browser extension:
1. Install ConsoleSpy extension from Chrome Web Store
2. Or load the extension folder as developer extension in Chrome

## 2. Configure Environment Variables

No additional configuration required.

## 3. Launch the Server

After completing the above steps, click the **Install** button below to connect and start the service.

Launch command:
```bash
node mcp-server.js
```

Or use the start script:
```bash
./start-servers.sh
```
