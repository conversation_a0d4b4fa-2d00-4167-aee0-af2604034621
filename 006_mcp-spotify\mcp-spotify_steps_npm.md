MCP Spotify - NPM 全局安装启动步骤

1. 安装Node.js和npm
- 访问 https://nodejs.org/ 下载并安装Node.js 18.0+
- npm通常随Node.js一起安装
- 验证安装：`node --version` 和 `npm --version`

2. 全局安装Spotify MCP包
- 运行 `npm install -g @thomaswawra/artistlens`
- 等待安装完成
- 验证安装：`artistlens --version`

3. 获取Spotify API凭据
- 访问 Spotify Developer Dashboard (https://developer.spotify.com/dashboard)
- 创建新应用
- 获取Client ID和Client Secret
- 配置重定向URI（如果需要）

4. 设置环境变量
- 设置 `SPOTIFY_CLIENT_ID` 为Client ID
- 设置 `SPOTIFY_CLIENT_SECRET` 为Client Secret
- 确认环境变量设置正确

5. 验证全局安装
- 运行 `npm list -g @thomaswawra/artistlens` 确认安装成功
- 检查全局包路径

6. 启动Spotify MCP服务
- 运行 `artistlens`
- 服务将直接启动

7. 验证服务运行
- 确认服务正常启动
- 通过MCP客户端测试Spotify功能

