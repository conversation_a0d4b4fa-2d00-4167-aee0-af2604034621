AWS Pricing MCP - Lambda 部署启动步骤

1. 安装AWS工具和Python
- 访问 https://aws.amazon.com/cli/ 下载并安装AWS CLI
- 访问 https://docs.aws.amazon.com/serverless-application-model/latest/developerguide/install-sam-cli.html 安装SAM CLI
- 访问 https://python.org/ 下载并安装Python 3.8+
- 验证安装：`aws --version`、`sam --version` 和 `python --version`

2. 配置AWS凭据
- 运行 `aws configure` 配置AWS访问密钥
- 输入Access Key ID、Secret Access Key、默认区域
- 或设置环境变量：`AWS_ACCESS_KEY_ID`、`AWS_SECRET_ACCESS_KEY`、`AWS_DEFAULT_REGION`

3. 克隆源码并进入目录
- 运行 `git clone https://github.com/trilogy-group/aws-pricing-mcp.git`
- 进入项目目录：`cd aws-pricing-mcp`

4. 构建SAM应用
- 运行 `sam build` 构建应用
- 等待构建完成
- 确认构建成功

5. 部署到AWS Lambda
- 运行 `sam deploy --guided` 进行引导式部署
- 按提示输入堆栈名称、区域等配置
- 确认部署参数并执行部署

6. 验证Lambda部署
- 在AWS控制台中检查Lambda函数
- 测试函数是否正常运行
- 获取函数的调用URL或API Gateway端点

7. 验证服务运行
- 通过API端点测试服务
- 通过MCP客户端测试AWS定价查询功能
