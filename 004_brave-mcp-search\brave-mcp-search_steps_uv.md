Brave MCP Search - UV 启动步骤

1. 安装Python和uv
- 访问 https://python.org/ 下载并安装Python 3.11+
- 运行 `pip install uv` 安装uv包管理器
- 验证安装：`python --version` 和 `uv --version`

2. 克隆源码并设置环境
- 运行 `git clone https://github.com/arben-adm/brave-mcp-search.git`
- 进入项目目录：`cd brave-mcp-search`
- 创建虚拟环境：`uv venv`

3. 激活环境并安装依赖
- 激活虚拟环境：`.venv\Scripts\activate` (Windows) 或 `source .venv/bin/activate` (Linux/Mac)
- 安装依赖：`uv pip install -r requirements.txt`

4. 获取Brave Search API密钥
- 访问 Brave Search API 官网注册账户
- 创建新的API密钥
- 复制生成的API密钥
- 确认API配额和使用限制

5. 设置环境变量
- 设置 `BRAVE_API_KEY` 为获取的API密钥
- 确认环境变量设置正确

6. 启动Brave Search MCP服务
- 运行 `uv --directory path-to/brave-mcp-search/src run server.py`
- 确保使用项目的绝对路径

7. 验证服务运行
- 确认服务正常启动
- 通过MCP客户端测试搜索功能

